<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> Türet<PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #f3f4f6 0%, #dbeafe 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .game-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
        }

        .loading-screen {
            text-align: center;
            padding: 20px;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .game-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .game-stats {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            padding: 10px;
            background: #f8fafc;
            border-radius: 10px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .input-container {
            margin: 20px 0;
        }

        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus {
            outline: none;
            border-color: #93c5fd;
        }

        .words-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 20px 0;
        }

        .word-item {
            background: #dbeafe;
            color: #1e40af;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
        }

        .error-message {
            color: #dc2626;
            background: #fee2e2;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            display: none;
        }

        .start-button, .play-again-button {
            background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            transition: opacity 0.3s;
        }

        .start-button:hover, .play-again-button:hover {
            opacity: 0.9;
        }

        .game-over {
            text-align: center;
            display: none;
        }

        .game-over h2 {
            color: #4f46e5;
            margin-bottom: 10px;
        }

        #gameContent {
            display: none;
        }

        /* Yeni eklenen stil */
        .word-list {
            margin-top: 20px;
            text-align: left;
            background: #f8fafc;
            padding: 15px;
            border-radius: 10px;
            max-height: 200px;
            overflow-y: auto;
        }

        .word-list-item {
            display: flex;
            justify-content: space-between;
            padding: 8px;
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
        }

        .word-list-item:last-child {
            border-bottom: none;
        }

        .word-list-item span {
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div id="loadingScreen" class="loading-screen">
            <h2>Kelime Listesi Yükleniyor...</h2>
            <div class="loading-spinner"></div>
        </div>

        <div id="gameContent">
            <div class="game-header">
                <h1>Kelime Türetme Oyunu</h1>
            </div>

            <div class="game-stats">
                <div class="stat-item">
                    <span>⏱️</span>
                    <span id="timeLeft">60</span>s
                </div>
                <div class="stat-item">
                    <span>⭐</span>
                    <span id="score">0</span>
                </div>
            </div>

            <div id="gamePlay" style="display: none;">
                <div class="input-container">
                    <input 
                        type="text" 
                        id="wordInput" 
                        placeholder="Kelime girin..."
                        autocomplete="off"
                    >
                </div>
                <div id="errorMessage" class="error-message"></div>
                <div id="wordsContainer" class="words-container"></div>
            </div>

            <button id="startButton" class="start-button">Oyunu Başlat</button>

            <div id="gameOver" class="game-over">
                <h2>Oyun Bitti!</h2>
                <p>Toplam Puan: <span id="finalScore">0</span></p>
                <p>Bulunan Kelime Sayısı: <span id="wordCount">0</span></p>
                <div id="wordList" class="word-list"></div>
                <button class="play-again-button" onclick="game.startGame()">Tekrar Oyna</button>
            </div>
        </div>
    </div>

    <script>
        class WordGame {
            constructor() {
                this.words = new Set();
                this.usedWords = [];
                this.wordScores = []; // Kelimelerin puanlarını saklamak için
                this.score = 0;
                this.timeLeft = 60;
                this.isPlaying = false;
                this.timer = null;

                this.elements = {
                    timeLeft: document.getElementById('timeLeft'),
                    score: document.getElementById('score'),
                    wordInput: document.getElementById('wordInput'),
                    errorMessage: document.getElementById('errorMessage'),
                    wordsContainer: document.getElementById('wordsContainer'),
                    gamePlay: document.getElementById('gamePlay'),
                    startButton: document.getElementById('startButton'),
                    gameOver: document.getElementById('gameOver'),
                    finalScore: document.getElementById('finalScore'),
                    wordCount: document.getElementById('wordCount'),
                    loadingScreen: document.getElementById('loadingScreen'),
                    gameContent: document.getElementById('gameContent'),
                    wordList: document.getElementById('wordList')
                };

                this.elements.startButton.addEventListener('click', () => this.startGame());
                this.elements.wordInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.submitWord();
                    }
                });

                this.loadWordList();
            }

            async loadWordList() {
                try {
                    const response = await fetch('https://raw.githubusercontent.com/CanNuhlar/Turkce-Kelime-Listesi/refs/heads/master/turkce_kelime_listesi.txt');
                    const text = await response.text();
                    
                    // Kelime listesini işle
                    const wordList = text.split('\n')
                        .map(word => word.trim())
                        .filter(word => word && word.length > 1); // Boş satırları ve tek harfleri filtrele
                    
                    this.words = new Set(wordList);
                    
                    // Yükleme ekranını kaldır ve oyunu göster
                    this.elements.loadingScreen.style.display = 'none';
                    this.elements.gameContent.style.display = 'block';
                } catch (error) {
                    console.error('Kelime listesi yüklenirken hata oluştu:', error);
                    this.elements.loadingScreen.innerHTML = `
                        <h2>Hata!</h2>
                        <p>Kelime listesi yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin.</p>
                    `;
                }
            }

            startGame() {
                this.isPlaying = true;
                this.usedWords = [];
                this.wordScores = []; // Puan listesini sıfırla
                this.score = 0;
                this.timeLeft = 60;
                
                this.updateDisplay();
                this.elements.gamePlay.style.display = 'block';
                this.elements.startButton.style.display = 'none';
                this.elements.gameOver.style.display = 'none';
                this.elements.errorMessage.style.display = 'none';
                this.elements.wordInput.focus();

                this.timer = setInterval(() => {
                    this.timeLeft--;
                    this.updateDisplay();

                    if (this.timeLeft <= 0) {
                        this.endGame();
                    }
                }, 1000);
            }

            submitWord() {
                const userLocale = navigator.language || 'tr-TR';
                const word = this.elements.wordInput.value.trim().toLocaleLowerCase(userLocale); 
                
                if (!word) {
                    this.showError('Lütfen bir kelime girin!');
                    return;
                }

                if (this.usedWords.includes(word)) {
                    this.showError('Bu kelime daha önce kullanıldı!');
                    return;
                }

                if (this.usedWords.length > 0) {
                    const lastWord = this.usedWords[this.usedWords.length - 1];
                    if (lastWord[lastWord.length - 1] !== word[0]) {
                        this.showError('Kelime, önceki kelimenin son harfi ile başlamalı!');
                        return;
                    }
                }

                if (!this.words.has(word)) {
                    this.showError('Geçersiz kelime!');
                    return;
                }

                this.addWord(word);
                this.elements.wordInput.value = '';
                this.elements.errorMessage.style.display = 'none';
            }

            addWord(word) {
                const points = this.calculatePoints(word);
                this.usedWords.push(word);
                this.wordScores.push({ word, points }); // Kelime ve puanı kaydet
                this.score += points;
                this.updateWordsDisplay();
                this.updateDisplay();
            }

            calculatePoints(word) {
                let points = word.length * 10;
                
                if (word.match(/[çşğüöıİ]/g)) {
                    points += 15;
                }
                
                if (word.length > 6) {
                    points += 20;
                }

                return points;
            }

            updateWordsDisplay() {
                this.elements.wordsContainer.innerHTML = this.usedWords
                    .map(word => `<span class="word-item">${word}</span>`)
                    .join('');
            }

            showError(message) {
                this.elements.errorMessage.textContent = message;
                this.elements.errorMessage.style.display = 'block';
                setTimeout(() => {
                    this.elements.errorMessage.style.display = 'none';
                }, 3000);
            }

            updateDisplay() {
                this.elements.timeLeft.textContent = this.timeLeft;
                this.elements.score.textContent = this.score;
            }

            endGame() {
                clearInterval(this.timer);
                this.isPlaying = false;
                
                this.elements.gamePlay.style.display = 'none';
                this.elements.gameOver.style.display = 'block';
                this.elements.finalScore.textContent = this.score;
                this.elements.wordCount.textContent = this.usedWords.length;

                // Kelime listesini ve puanları göster
                this.elements.wordList.innerHTML = this.wordScores
                    .map(item => `
                        <div class="word-list-item">
                            <span>${item.word}</span>
                            <span>${item.points} puan</span>
                        </div>
                    `)
                    .join('');
            }
        }

        const game = new WordGame();
    </script>
</body>
</html>