-- <PERSON><PERSON><PERSON> PostgreSQL Database Initialization Script
-- This script creates the initial database structure for PostgreSQL

-- Create database if it doesn't exist
-- Note: This should be run as superuser or database owner

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Create custom types for enums
DO $$ BEGIN
    CREATE TYPE game_difficulty AS ENUM ('Easy', 'Medium', 'Hard');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE game_status AS ENUM ('Waiting', 'InProgress', 'Finished', 'Cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE word_category AS ENUM ('General', 'Animal', 'Food', 'Place', 'Object', 'Person', 'Action', 'Adjective', 'Science', 'Technology', 'Sports', 'Art', 'Nature');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE word_difficulty AS ENUM ('Easy', 'Medium', 'Hard');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE word_validation_status AS ENUM ('Pending', 'Approved', 'Rejected', 'UnderReview');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE achievement_type AS ENUM ('GamesPlayed', 'Score', 'WinStreak', 'WordsUsed', 'ResponseTime', 'DailyActivity', 'Accuracy');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance (will be created after EF migrations)
-- These are PostgreSQL-optimized indexes

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function for Turkish text search
CREATE OR REPLACE FUNCTION turkish_word_search(word_text TEXT, search_term TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN unaccent(lower(word_text)) LIKE '%' || unaccent(lower(search_term)) || '%';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to calculate word points (PostgreSQL version)
CREATE OR REPLACE FUNCTION calculate_word_points(
    word_length INTEGER,
    word_frequency DECIMAL,
    response_time_ms INTEGER,
    game_difficulty game_difficulty
) RETURNS INTEGER AS $$
DECLARE
    base_points INTEGER;
    time_bonus INTEGER;
    difficulty_multiplier DECIMAL;
    total_points INTEGER;
BEGIN
    -- Base points calculation
    base_points := CASE 
        WHEN word_length <= 4 THEN 1
        WHEN word_length <= 6 THEN 2
        WHEN word_length <= 8 THEN 3
        ELSE 4
    END;
    
    -- Frequency bonus (rare words get more points)
    IF word_frequency < 0.001 THEN
        base_points := base_points + 2;
    ELSIF word_frequency < 0.01 THEN
        base_points := base_points + 1;
    END IF;
    
    -- Time bonus (faster response = more points)
    time_bonus := CASE 
        WHEN response_time_ms <= 3000 THEN 3
        WHEN response_time_ms <= 5000 THEN 2
        WHEN response_time_ms <= 10000 THEN 1
        ELSE 0
    END;
    
    -- Difficulty multiplier
    difficulty_multiplier := CASE game_difficulty
        WHEN 'Easy' THEN 1.0
        WHEN 'Medium' THEN 1.5
        WHEN 'Hard' THEN 2.0
    END;
    
    total_points := FLOOR((base_points + time_bonus) * difficulty_multiplier);
    
    RETURN GREATEST(1, total_points); -- Minimum 1 point
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to get user statistics
CREATE OR REPLACE FUNCTION get_user_statistics(user_id_param INTEGER)
RETURNS TABLE (
    user_id INTEGER,
    username VARCHAR(50),
    total_games_played INTEGER,
    games_won INTEGER,
    games_lost INTEGER,
    total_score BIGINT,
    highest_score INTEGER,
    win_rate DECIMAL,
    accuracy_rate DECIMAL,
    total_achievements INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.user_id,
        u.username,
        s.total_games_played,
        s.games_won,
        s.games_lost,
        s.total_score,
        s.highest_score,
        CASE WHEN s.total_games_played > 0 
             THEN ROUND((s.games_won::DECIMAL / s.total_games_played * 100), 2)
             ELSE 0 
        END as win_rate,
        CASE WHEN s.total_words_used > 0 
             THEN ROUND((s.valid_words::DECIMAL / s.total_words_used * 100), 2)
             ELSE 0 
        END as accuracy_rate,
        (SELECT COUNT(*) FROM user_achievements ua WHERE ua.user_id = user_id_param AND ua.is_completed = true)::INTEGER as total_achievements
    FROM statistics s
    INNER JOIN users u ON s.user_id = u.id
    WHERE s.user_id = user_id_param;
END;
$$ LANGUAGE plpgsql;

-- Function to get leaderboard
CREATE OR REPLACE FUNCTION get_leaderboard(
    leaderboard_type VARCHAR(20) DEFAULT 'Global',
    page_size INTEGER DEFAULT 20,
    page_number INTEGER DEFAULT 1
)
RETURNS TABLE (
    position BIGINT,
    user_id INTEGER,
    username VARCHAR(50),
    avatar TEXT,
    total_score BIGINT,
    games_won INTEGER,
    total_games_played INTEGER,
    win_rate DECIMAL,
    games_this_period INTEGER
) AS $$
DECLARE
    offset_value INTEGER := (page_number - 1) * page_size;
BEGIN
    IF leaderboard_type = 'Weekly' THEN
        RETURN QUERY
        SELECT 
            ROW_NUMBER() OVER (ORDER BY s.weekly_rank) as position,
            u.id as user_id,
            u.username,
            u.avatar,
            s.total_score,
            s.games_won,
            s.total_games_played,
            CASE WHEN s.total_games_played > 0 
                 THEN ROUND((s.games_won::DECIMAL / s.total_games_played * 100), 2)
                 ELSE 0 
            END as win_rate,
            s.weekly_games_played as games_this_period
        FROM statistics s
        INNER JOIN users u ON s.user_id = u.id
        WHERE u.is_active = true AND s.weekly_games_played > 0
        ORDER BY s.weekly_rank
        LIMIT page_size OFFSET offset_value;
    ELSIF leaderboard_type = 'Monthly' THEN
        RETURN QUERY
        SELECT 
            ROW_NUMBER() OVER (ORDER BY s.monthly_rank) as position,
            u.id as user_id,
            u.username,
            u.avatar,
            s.total_score,
            s.games_won,
            s.total_games_played,
            CASE WHEN s.total_games_played > 0 
                 THEN ROUND((s.games_won::DECIMAL / s.total_games_played * 100), 2)
                 ELSE 0 
            END as win_rate,
            s.monthly_games_played as games_this_period
        FROM statistics s
        INNER JOIN users u ON s.user_id = u.id
        WHERE u.is_active = true AND s.monthly_games_played > 0
        ORDER BY s.monthly_rank
        LIMIT page_size OFFSET offset_value;
    ELSE -- Global
        RETURN QUERY
        SELECT 
            ROW_NUMBER() OVER (ORDER BY s.global_rank) as position,
            u.id as user_id,
            u.username,
            u.avatar,
            s.total_score,
            s.games_won,
            s.total_games_played,
            CASE WHEN s.total_games_played > 0 
                 THEN ROUND((s.games_won::DECIMAL / s.total_games_played * 100), 2)
                 ELSE 0 
            END as win_rate,
            s.total_games_played as games_this_period
        FROM statistics s
        INNER JOIN users u ON s.user_id = u.id
        WHERE u.is_active = true AND s.total_games_played > 0
        ORDER BY s.global_rank
        LIMIT page_size OFFSET offset_value;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Sample achievements data
INSERT INTO achievements (name, description, icon, category, points, type, required_value, is_active, created_at)
VALUES 
    ('İlk Adım', 'İlk oyununuzu tamamlayın', '🎯', 'Başlangıç', 10, 'GamesPlayed', 1, true, CURRENT_TIMESTAMP),
    ('Kelime Ustası', '100 geçerli kelime kullanın', '📚', 'Kelime', 50, 'WordsUsed', 100, true, CURRENT_TIMESTAMP),
    ('Hızlı Düşünür', '5 saniyede kelime bulun', '⚡', 'Hız', 25, 'ResponseTime', 5, true, CURRENT_TIMESTAMP),
    ('Kazanan', '10 oyun kazanın', '🏆', 'Zafer', 100, 'GamesPlayed', 10, true, CURRENT_TIMESTAMP),
    ('Seri Kazanan', '5 oyun üst üste kazanın', '🔥', 'Seri', 200, 'WinStreak', 5, true, CURRENT_TIMESTAMP),
    ('Günlük Oyuncu', 'Bir günde 5 oyun oynayın', '📅', 'Aktivite', 30, 'DailyActivity', 5, true, CURRENT_TIMESTAMP),
    ('Mükemmeliyetçi', '%90 doğruluk oranına ulaşın', '💎', 'Doğruluk', 150, 'Accuracy', 90, true, CURRENT_TIMESTAMP),
    ('Sosyal Oyuncu', '20 farklı oyuncu ile oynayın', '👥', 'Sosyal', 75, 'GamesPlayed', 20, true, CURRENT_TIMESTAMP),
    ('Kelime Avcısı', '500 geçerli kelime kullanın', '🎯', 'Kelime', 250, 'WordsUsed', 500, true, CURRENT_TIMESTAMP),
    ('Efsane', '1000 puan skoruna ulaşın', '⭐', 'Skor', 500, 'Score', 1000, true, CURRENT_TIMESTAMP)
ON CONFLICT (name) DO NOTHING;

-- Create performance indexes (will be applied after EF creates tables)
-- These will be created by a separate migration or startup script

COMMENT ON DATABASE current_database() IS 'Kelime Zinciri Game Database - PostgreSQL Version';
