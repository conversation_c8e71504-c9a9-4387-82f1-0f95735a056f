-- <PERSON><PERSON>e Zinciri Database Initialization Script
-- This script creates the initial database structure

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'KelimeZinciriDB')
BEGIN
    CREATE DATABASE KelimeZinciriDB;
END
GO

USE KelimeZinciriDB;
GO

-- Enable snapshot isolation for better concurrency
ALTER DATABASE KelimeZinciriDB SET ALLOW_SNAPSHOT_ISOLATION ON;
ALTER DATABASE KelimeZinciriDB SET READ_COMMITTED_SNAPSHOT ON;
GO

-- Create indexes for better performance after tables are created by EF
-- These will be created after the application starts and runs migrations

-- Sample achievements data
IF NOT EXISTS (SELECT 1 FROM Achievements WHERE Name = 'İlk Adım')
BEGIN
    INSERT INTO Achievements (Name, Description, Icon, Category, Points, Type, RequiredValue, IsActive, CreatedAt)
    VALUES 
    ('İlk Adım', '<PERSON>lk oyununuz<PERSON> ta<PERSON>', '🎯', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 10, 1, 1, 1, GETUTCDATE()),
    ('Kelime Ustası', '100 geçerli kelime kullanın', '📚', 'Kelime', 50, 4, 100, 1, GETUTCDATE()),
    ('Hızlı Düşünür', '5 saniyede kelime bulun', '⚡', 'Hız', 25, 5, 5, 1, GE<PERSON>TCDATE()),
    ('<PERSON>an', '10 oyun kazanın', '🏆', 'Zafer', 100, 1, 10, 1, GETU<PERSON>DATE()),
    ('Seri Kazanan', '5 oyun üst üste kazanın', '🔥', 'Seri', 200, 3, 5, 1, GETUTCDATE()),
    ('Günlük Oyuncu', 'Bir günde 5 oyun oynayın', '📅', 'Aktivite', 30, 6, 5, 1, GETUTCDATE()),
    ('Mükemmeliyetçi', '%90 doğruluk oranına ulaşın', '💎', 'Doğruluk', 150, 7, 90, 1, GETUTCDATE()),
    ('Sosyal Oyuncu', '20 farklı oyuncu ile oynayın', '👥', 'Sosyal', 75, 1, 20, 1, GETUTCDATE()),
    ('Kelime Avcısı', '500 geçerli kelime kullanın', '🎯', 'Kelime', 250, 4, 500, 1, GETUTCDATE()),
    ('Efsane', '1000 puan skoruna ulaşın', '⭐', 'Skor', 500, 2, 1000, 1, GETUTCDATE());
END
GO

-- Create stored procedures for common operations

-- Get user statistics
CREATE OR ALTER PROCEDURE GetUserStatistics
    @UserId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        s.*,
        u.Username,
        u.CreatedAt as UserCreatedAt,
        (SELECT COUNT(*) FROM GamePlayers gp 
         INNER JOIN Games g ON gp.GameId = g.Id 
         WHERE gp.UserId = @UserId AND g.Status = 2) as TotalGamesCompleted,
        (SELECT COUNT(*) FROM UserAchievements ua WHERE ua.UserId = @UserId AND ua.IsCompleted = 1) as CompletedAchievements
    FROM Statistics s
    INNER JOIN Users u ON s.UserId = u.Id
    WHERE s.UserId = @UserId;
END
GO

-- Get leaderboard
CREATE OR ALTER PROCEDURE GetLeaderboard
    @Type NVARCHAR(20) = 'Global', -- Global, Weekly, Monthly
    @PageSize INT = 20,
    @PageNumber INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    IF @Type = 'Weekly'
    BEGIN
        SELECT 
            ROW_NUMBER() OVER (ORDER BY s.WeeklyRank) as Position,
            u.Id,
            u.Username,
            u.Avatar,
            s.TotalScore,
            s.GamesWon,
            s.TotalGamesPlayed,
            s.WinRate,
            s.WeeklyGamesPlayed as GamesThisPeriod
        FROM Statistics s
        INNER JOIN Users u ON s.UserId = u.Id
        WHERE u.IsActive = 1 AND s.WeeklyGamesPlayed > 0
        ORDER BY s.WeeklyRank
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
    END
    ELSE IF @Type = 'Monthly'
    BEGIN
        SELECT 
            ROW_NUMBER() OVER (ORDER BY s.MonthlyRank) as Position,
            u.Id,
            u.Username,
            u.Avatar,
            s.TotalScore,
            s.GamesWon,
            s.TotalGamesPlayed,
            s.WinRate,
            s.MonthlyGamesPlayed as GamesThisPeriod
        FROM Statistics s
        INNER JOIN Users u ON s.UserId = u.Id
        WHERE u.IsActive = 1 AND s.MonthlyGamesPlayed > 0
        ORDER BY s.MonthlyRank
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
    END
    ELSE -- Global
    BEGIN
        SELECT 
            ROW_NUMBER() OVER (ORDER BY s.GlobalRank) as Position,
            u.Id,
            u.Username,
            u.Avatar,
            s.TotalScore,
            s.GamesWon,
            s.TotalGamesPlayed,
            s.WinRate,
            s.TotalGamesPlayed as GamesThisPeriod
        FROM Statistics s
        INNER JOIN Users u ON s.UserId = u.Id
        WHERE u.IsActive = 1 AND s.TotalGamesPlayed > 0
        ORDER BY s.GlobalRank
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
    END
END
GO

-- Update user statistics after game
CREATE OR ALTER PROCEDURE UpdateUserStatistics
    @UserId INT,
    @GameResult NVARCHAR(10), -- 'Won', 'Lost', 'Draw'
    @Score INT,
    @WordsUsed INT,
    @ValidWords INT,
    @GameDuration INT, -- in seconds
    @AverageResponseTime INT -- in milliseconds
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION;
    
    TRY
        -- Update statistics
        UPDATE Statistics 
        SET 
            TotalGamesPlayed = TotalGamesPlayed + 1,
            GamesWon = CASE WHEN @GameResult = 'Won' THEN GamesWon + 1 ELSE GamesWon END,
            GamesLost = CASE WHEN @GameResult = 'Lost' THEN GamesLost + 1 ELSE GamesLost END,
            GamesDraw = CASE WHEN @GameResult = 'Draw' THEN GamesDraw + 1 ELSE GamesDraw END,
            TotalScore = TotalScore + @Score,
            HighestScore = CASE WHEN @Score > HighestScore THEN @Score ELSE HighestScore END,
            TotalWordsUsed = TotalWordsUsed + @WordsUsed,
            ValidWords = ValidWords + @ValidWords,
            InvalidWords = InvalidWords + (@WordsUsed - @ValidWords),
            TotalPlayTime = DATEADD(SECOND, @GameDuration, TotalPlayTime),
            AverageResponseTime = CASE 
                WHEN TotalGamesPlayed = 0 THEN @AverageResponseTime 
                ELSE (AverageResponseTime * TotalGamesPlayed + @AverageResponseTime) / (TotalGamesPlayed + 1) 
            END,
            CurrentWinStreak = CASE 
                WHEN @GameResult = 'Won' THEN CurrentWinStreak + 1 
                ELSE 0 
            END,
            LongestWinStreak = CASE 
                WHEN @GameResult = 'Won' AND CurrentWinStreak + 1 > LongestWinStreak 
                THEN CurrentWinStreak + 1 
                ELSE LongestWinStreak 
            END,
            CurrentLossStreak = CASE 
                WHEN @GameResult = 'Lost' THEN CurrentLossStreak + 1 
                ELSE 0 
            END,
            LastGameDate = GETUTCDATE(),
            DailyGamesPlayed = DailyGamesPlayed + 1,
            WeeklyGamesPlayed = WeeklyGamesPlayed + 1,
            MonthlyGamesPlayed = MonthlyGamesPlayed + 1,
            UpdatedAt = GETUTCDATE()
        WHERE UserId = @UserId;
        
        -- Update calculated fields
        UPDATE Statistics 
        SET 
            AverageScore = CASE WHEN TotalGamesPlayed > 0 THEN TotalScore / TotalGamesPlayed ELSE 0 END
        WHERE UserId = @UserId;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- Reset daily/weekly/monthly counters (to be called by scheduled job)
CREATE OR ALTER PROCEDURE ResetPeriodCounters
    @Period NVARCHAR(10) -- 'Daily', 'Weekly', 'Monthly'
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @Period = 'Daily'
    BEGIN
        UPDATE Statistics SET DailyGamesPlayed = 0;
    END
    ELSE IF @Period = 'Weekly'
    BEGIN
        UPDATE Statistics SET WeeklyGamesPlayed = 0;
    END
    ELSE IF @Period = 'Monthly'
    BEGIN
        UPDATE Statistics SET MonthlyGamesPlayed = 0;
    END
END
GO

PRINT 'Database initialization completed successfully!';
GO
