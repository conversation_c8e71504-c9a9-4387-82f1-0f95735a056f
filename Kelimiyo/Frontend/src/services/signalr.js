import { HubConnectionBuilder, LogLevel } from '@microsoft/signalr';

/**
 * Professional SignalR Service
 * Clean, reliable, and maintainable SignalR implementation
 * 
 * <AUTHOR> Developer
 * @version 2.0
 */
class SignalRService {
  constructor() {
    this.connection = null;
    this.isConnected = false;
    this.baseUrl =   import.meta.env.VITE_SIGNALR_URL || 'https://localhost:7297/gamehub';

    // Connection state tracking
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 3;

    // Handler references to prevent duplicates
    this._gameHandlers = null;
  }

  /**
   * Establish SignalR connection with authentication
   * @param {string} token - JWT authentication token
   */
  async connect(token = null) {
    if (this.isConnectionActive()) {
      console.log('✅ SignalR already connected');
      return;
    }

    if (!token) {
      token = localStorage.getItem('token');
    }

    if (!token) {
      console.warn('⚠️ No token found, will retry with fresh token');
      // Wait a bit and try to get token again (for page refresh scenarios)
      await new Promise(resolve => setTimeout(resolve, 500));
      token = localStorage.getItem('token');

      if (!token) {
        throw new Error('Authentication token bulunamadı');
      }
    }

    try {
      console.log('🔌 Connecting to SignalR...');
      console.log('🔌 SignalR URL:', this.baseUrl);
      console.log('🔌 Token exists:', !!token);

      // Clean up any existing connection
      await this.disconnect();

      this.connection = new HubConnectionBuilder()
        .withUrl(this.baseUrl, {
          accessTokenFactory: () => {
            // Always get fresh token from localStorage
            const freshToken = localStorage.getItem('token') || token;
            console.log('🔑 Providing token for SignalR, fresh token exists:', !!freshToken);
            return freshToken;
          }
        })
        .withAutomaticReconnect([0, 2000, 10000, 30000])
        .configureLogging(LogLevel.Information)
        .build();

      this._setupConnectionEvents();
      // _setupGameEvents() removed - components handle events directly

      console.log('🔌 Starting SignalR connection...');
      await this.connection.start();
      this.isConnected = true;
      this.reconnectAttempts = 0;

      console.log('✅ SignalR connected successfully');
      console.log('✅ Connection state:', this.connection.state);

    } catch (error) {
      console.error('❌ SignalR connection failed:', error);
      console.error('❌ Error details:', error.message);
      this.isConnected = false;
      throw error;
    }
  }

  /**
   * Setup connection lifecycle events
   * @private
   */
  _setupConnectionEvents() {
    this.connection.onreconnecting(() => {
      console.log('🔄 SignalR reconnecting...');
      this.isConnected = false;
    });

    this.connection.onreconnected(() => {
      console.log('✅ SignalR reconnected');
      this.isConnected = true;
      this.reconnectAttempts = 0;

      // DO NOT setup events again - they are already setup
      // Components will handle their own reconnection logic
      console.log('🔄 Reconnected - handlers already in place');
    });

    this.connection.onclose((error) => {
      console.log('🔌 SignalR connection closed');
      this.isConnected = false;
      if (error) {
        console.error('Connection error:', error);
      }
    });
  }

  // _setupGameEvents() and _clearSignalRHandlers() removed
  // Components handle their own event registration/cleanup directly

  // _emit method removed - components handle events directly

  // on/off methods removed - components register directly to SignalR connection

  /**
   * Check if connection is active
   * @public
   */
  isConnectionActive() {
    return this.connection?.state === 'Connected';
  }

  /**
   * Get current connection state
   * @public
   */
  getConnectionState() {
    return this.connection?.state || 'Disconnected';
  }

  /**
   * Disconnect from SignalR
   * @public
   */
  async disconnect() {
    if (this.connection) {
      try {
        console.log('🔌 Disconnecting SignalR...');
        // Components handle their own cleanup
        await this.connection.stop();
        console.log('✅ SignalR disconnected');
      } catch (error) {
        console.error('❌ Error disconnecting:', error);
      } finally {
        this.connection = null;
        this.isConnected = false;
      }
    }
  }

  // ===========================================
  // GAME METHODS
  // ===========================================

  /**
   * Join a game room
   * @param {string} roomCode - Game room code
   */
  async joinGameRoom(roomCode) {
    this._ensureConnection();
    try {
      console.log(`🎮 Joining game room: ${roomCode}`);
      await this.connection.invoke('JoinGameRoom', roomCode);
      console.log(`✅ Successfully joined room: ${roomCode}`);
    } catch (error) {
      console.error('❌ Error joining game room:', error);
      throw error;
    }
  }

  /**
   * Leave a game room
   * @param {string} roomCode - Game room code
   */
  async leaveGameRoom(roomCode) {
    if (!this.isConnectionActive()) return;

    try {
      await this.connection.invoke('LeaveGameRoom', roomCode);
      console.log(`👋 Left game room: ${roomCode}`);
    } catch (error) {
      console.error('Error leaving game room:', error);
    }
  }

  /**
   * Start a game
   * @param {string} gameId - Game ID
   */
  async startGame(gameId) {
    this._ensureConnection();
    try {
      await this.connection.invoke('StartGame', gameId);
      console.log(`🚀 Game started: ${gameId}`);
    } catch (error) {
      console.error('Error starting game:', error);
      throw error;
    }
  }

  /**
   * Submit a word
   * @param {string} gameId - Game ID
   * @param {string} word - Word to submit
   */
  async submitWord(gameId, word) {
    this._ensureConnection();
    try {
      await this.connection.invoke('SubmitWord', gameId, word);
      console.log(`📝 Word submitted: ${word}`);
    } catch (error) {
      console.error('Error submitting word:', error);
      throw error;
    }
  }

  /**
   * Send chat message
   * @param {string} gameId - Game ID
   * @param {string} message - Chat message
   */
  async sendChatMessage(gameId, message) {
    this._ensureConnection();
    try {
      await this.connection.invoke('SendChatMessage', gameId, message);
    } catch (error) {
      console.error('Error sending chat message:', error);
      throw error;
    }
  }

  /**
   * Request current game state
   * @param {string} gameId - Game ID
   */
  async requestGameState(gameId) {
    this._ensureConnection();
    try {
      await this.connection.invoke('RequestGameState', gameId);
    } catch (error) {
      console.error('Error requesting game state:', error);
      throw error;
    }
  }

  /**
   * Send typing status
   * @param {string} gameId - Game ID
   * @param {boolean} isTyping - Typing status
   */
  async playerTyping(gameId, isTyping) {
    if (!this.isConnectionActive()) return;

    try {
      await this.connection.invoke('PlayerTyping', gameId, isTyping);
    } catch (error) {
      console.error('Error sending typing status:', error);
    }
  }

  /**
   * Send typing indicator for room
   * @param {string} roomCode - Room code
   * @param {boolean} isTyping - Typing status
   */
  async sendTypingIndicator(roomCode, isTyping) {
    if (!this.isConnectionActive()) return;

    try {
      await this.connection.invoke('SendTypingIndicator', roomCode, isTyping);
    } catch (error) {
      console.error('Error sending typing indicator:', error);
    }
  }

  /**
   * Ensure connection is active, throw error if not
   * @private
   */
  _ensureConnection() {
    if (!this.isConnectionActive()) {
      throw new Error('SignalR bağlantısı yok');
    }
  }
}

// Create and export singleton instance
const signalRService = new SignalRService();
export default signalRService;
