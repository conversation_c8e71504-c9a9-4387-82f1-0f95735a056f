import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { gameAPI } from '../services/api';
import signalRService from '../services/signalr';
import toast from 'react-hot-toast';

// Initial state
const initialState = {
  currentGame: null,
  gameState: 'idle', // idle, waiting, playing, finished
  players: [],
  currentPlayer: null,
  gameHistory: [],
  lastWord: null,
  timeRemaining: 0,
  isLoading: false,
  error: null,
};

// Action types
const GAME_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  SET_GAME: 'SET_GAME',
  UPDATE_GAME_STATE: 'UPDATE_GAME_STATE',
  ADD_PLAYER: 'ADD_PLAYER',
  REMOVE_PLAYER: 'REMOVE_PLAYER',
  UPDATE_PLAYER: 'UPDATE_PLAYER',
  SET_CURRENT_PLAYER: 'SET_CURRENT_PLAYER',
  ADD_WORD: 'ADD_WORD',
  SET_TIME_REMAINING: 'SET_TIME_REMAINING',
  RESET_GAME: 'RESET_GAME',
};

// Reducer
function gameReducer(state, action) {
  switch (action.type) {
    case GAME_ACTIONS.SET_LOADING:
      return { ...state, isLoading: action.payload };

    case GAME_ACTIONS.SET_ERROR:
      return { ...state, error: action.payload, isLoading: false };

    case GAME_ACTIONS.CLEAR_ERROR:
      return { ...state, error: null };

    case GAME_ACTIONS.SET_GAME:
      return {
        ...state,
        currentGame: action.payload,
        gameState: action.payload?.status?.toLowerCase() || 'idle',
        players: action.payload?.players || [],
        isLoading: false,
        error: null,
      };

    case GAME_ACTIONS.UPDATE_GAME_STATE:
      return { ...state, gameState: action.payload };

    case GAME_ACTIONS.ADD_PLAYER:
      return {
        ...state,
        players: [...state.players, action.payload],
      };

    case GAME_ACTIONS.REMOVE_PLAYER:
      return {
        ...state,
        players: state.players.filter(p => p.id !== action.payload),
      };

    case GAME_ACTIONS.UPDATE_PLAYER:
      return {
        ...state,
        players: state.players.map(p =>
          p.id === action.payload.id ? { ...p, ...action.payload } : p
        ),
      };

    case GAME_ACTIONS.SET_CURRENT_PLAYER:
      return { ...state, currentPlayer: action.payload };

    case GAME_ACTIONS.ADD_WORD:
      return {
        ...state,
        gameHistory: [...state.gameHistory, action.payload],
        lastWord: action.payload,
      };

    case GAME_ACTIONS.SET_TIME_REMAINING:
      return { ...state, timeRemaining: action.payload };

    case GAME_ACTIONS.RESET_GAME:
      return { ...initialState };

    default:
      return state;
  }
}

// Create context
const GameContext = createContext();

// Provider component
export function GameProvider({ children }) {
  const [state, dispatch] = useReducer(gameReducer, initialState);

  // NOTE: SignalR event handlers are managed by GamePage component
  // to prevent duplicate handlers and ensure proper cleanup

  // Actions
  const createGame = async (gameData) => {
    dispatch({ type: GAME_ACTIONS.SET_LOADING, payload: true });
    try {
      const response = await gameAPI.createRoom(gameData);
      if (response.success) {
        dispatch({ type: GAME_ACTIONS.SET_GAME, payload: response.data });
        return response;
      } else {
        dispatch({ type: GAME_ACTIONS.SET_ERROR, payload: response.message });
        return response;
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Oyun oluşturulamadı';
      dispatch({ type: GAME_ACTIONS.SET_ERROR, payload: errorMessage });
      return { success: false, message: errorMessage };
    }
  };

  const joinGame = async (roomCode) => {
    dispatch({ type: GAME_ACTIONS.SET_LOADING, payload: true });
    try {
      const response = await gameAPI.joinRoom(roomCode);
      if (response.success) {
        dispatch({ type: GAME_ACTIONS.SET_GAME, payload: response.data });
        return response;
      } else {
        dispatch({ type: GAME_ACTIONS.SET_ERROR, payload: response.message });
        return response;
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Oyuna katılınamadı';
      dispatch({ type: GAME_ACTIONS.SET_ERROR, payload: errorMessage });
      return { success: false, message: errorMessage };
    }
  };

  const leaveGame = async () => {
    if (!state.currentGame) return;

    try {
      await gameAPI.leaveGame(state.currentGame.id);
      dispatch({ type: GAME_ACTIONS.RESET_GAME });
    } catch (error) {
      console.error('Leave game error:', error);
    }
  };

  const submitWord = async (word) => {
    if (!state.currentGame) return;

    try {
      const response = await gameAPI.submitWord(state.currentGame.id, word);
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Kelime gönderilemedi';
      toast.error(errorMessage);
      return { success: false, message: errorMessage };
    }
  };

  const startGame = async () => {
    if (!state.currentGame) return;

    try {
      const response = await gameAPI.startGame(state.currentGame.id);
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Oyun başlatılamadı';
      dispatch({ type: GAME_ACTIONS.SET_ERROR, payload: errorMessage });
      return { success: false, message: errorMessage };
    }
  };

  const clearError = () => {
    dispatch({ type: GAME_ACTIONS.CLEAR_ERROR });
  };

  const resetGame = () => {
    dispatch({ type: GAME_ACTIONS.RESET_GAME });
  };

  const value = {
    // State
    currentGame: state.currentGame,
    gameState: state.gameState,
    players: state.players,
    currentPlayer: state.currentPlayer,
    gameHistory: state.gameHistory,
    lastWord: state.lastWord,
    timeRemaining: state.timeRemaining,
    isLoading: state.isLoading,
    error: state.error,

    // Actions
    createGame,
    joinGame,
    leaveGame,
    submitWord,
    startGame,
    clearError,
    resetGame,
  };

  return (
    <GameContext.Provider value={value}>
      {children}
    </GameContext.Provider>
  );
}

// Hook to use game context
export function useGame() {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
}

export default GameContext;
