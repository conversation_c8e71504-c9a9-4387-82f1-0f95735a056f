import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import { gameAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import signalRService from '../services/signalr';

const GamePage = () => {
  const { roomCode } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();

  const [game, setGame] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentWord, setCurrentWord] = useState('');
  const [gameState, setGameState] = useState('waiting');
  const [requiredStartLetter, setRequiredStartLetter] = useState(null);
  const [currentPlayerTurn, setCurrentPlayerTurn] = useState(null);
  const [recentMoves, setRecentMoves] = useState([]);
  const [gameTimer, setGameTimer] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [typingPlayers, setTypingPlayers] = useState([]);
  const [gameStats, setGameStats] = useState({
    totalWords: 0,
    averagePoints: 0,
    longestWord: '',
    fastestWord: null
  });

  // Store handler references to prevent duplicates
  const handlersRef = useRef(null);

  useEffect(() => {
    const initializeGame = async () => {
      if (roomCode && user?.id) {
        // Small delay to ensure auth is fully loaded
        await new Promise(resolve => setTimeout(resolve, 100));

        try {
          await loadGame();
          await setupSignalR();
        } catch (error) {
          console.error('Game initialization error:', error);
          toast.error('Oyun başlatılamadı');
        }
      }
    };

    initializeGame();

    return () => {
      cleanupSignalR();
    };
  }, [roomCode]); // Only depend on roomCode, not user?.id

  // Timer countdown effect
  useEffect(() => {
    if (timeRemaining > 0 && gameState === 'playing') {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1000) {
            setGameState('finished');
            toast.info('⏰ Süre doldu!');
            return 0;
          }
          return prev - 1000;
        });
      }, 1000);

      setGameTimer(timer);
      return () => clearInterval(timer);
    }
  }, [timeRemaining, gameState]);

  const loadGame = async () => {
    try {
      setLoading(true);
      const response = await gameAPI.getGameByRoomCode(roomCode);

      if (response.success && response.game) {
        setGame(response.game);
        const status = response.game.status.toLowerCase();
        if (status === 'inprogress') setGameState('playing');
        else if (status === 'waiting') setGameState('waiting');
        else if (status === 'finished' || status === 'completed') setGameState('finished');

        setRequiredStartLetter(response.game.requiredStartLetter);
        setCurrentPlayerTurn(response.game.currentPlayerTurnId);
        setRecentMoves(response.game.recentMoves || []);

        // Set timer if game has time limit
        if (response.game.timeLimit && response.game.startedAt) {
          const startTime = new Date(response.game.startedAt);
          const endTime = new Date(startTime.getTime() + response.game.timeLimit * 60000);
          const now = new Date();
          const remaining = Math.max(0, endTime.getTime() - now.getTime());
          setTimeRemaining(remaining);
        }

        // Calculate game stats
        if (response.game.recentMoves?.length > 0) {
          const moves = response.game.recentMoves;
          const totalPoints = moves.reduce((sum, move) => sum + (move.points || 0), 0);
          const longestWord = moves.reduce((longest, move) =>
            move.word.length > longest.length ? move.word : longest, '');

          setGameStats({
            totalWords: moves.length,
            averagePoints: moves.length > 0 ? Math.round(totalPoints / moves.length) : 0,
            longestWord,
            fastestWord: moves[0]?.word || null
          });
        }
      } else {
        toast.error('Oyun bulunamadı');
        navigate('/');
      }
    } catch (error) {
      console.error('Game load error:', error);
      toast.error('Oyun yüklenemedi');
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const setupSignalR = async () => {
    try {
      console.log('🔧 Setting up SignalR...');
      console.log('🔧 Current connection state:', signalRService.getConnectionState());

      // Prevent duplicate setup
      if (handlersRef.current && signalRService.isConnectionActive()) {
        console.log('⚠️ SignalR handlers already registered and connection active, skipping setup');
        return;
      }

      // Only cleanup if we already have handlers registered
      if (handlersRef.current) {
        console.log('🧹 Cleaning up existing handlers before setup...');
        cleanupSignalR();
      }

      if (!signalRService.isConnectionActive()) {
        console.log('🔌 SignalR not connected, connecting...');

        // Retry logic for connection
        await new Promise(resolve => setTimeout(resolve, 3000));

      
          try {
            const token = localStorage.getItem('token');
            if (!token) {
              console.warn('⚠️ No token found, waiting for auth...');
            }

            await signalRService.connect(token);
            console.log('✅ SignalR connected, state:', signalRService.getConnectionState());
            
          } catch (error) {
           
            console.warn(`⚠️ SignalR connection attempt ${retries} failed:`, error.message);

             

            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 2000 * retries));
          }
        
      } else {
        console.log('✅ SignalR already connected');
      }

      console.log('🚪 Joining room:', roomCode);
      await signalRService.joinGameRoom(roomCode);
      console.log('✅ Joined room:', roomCode);

      // Create handler functions
      const gameStartedHandler = (data) => {
        console.log('🎮 [GamePage] Game started event received:', data);
        setGameState('playing');
        toast.success('🎮 Oyun başladı!');
        loadGame();
      };

      const wordSubmittedHandler = (data) => {
        console.log('📝 [GamePage] Word submitted event received:', data);
        if (data.userId !== user?.id) {
          toast.success(`${data.username}: "${data.word}" (+${data.points} puan)`);
        }
        loadGame();
      };

      const wordRejectedHandler = (data) => {
        console.log('❌ [GamePage] Word rejected event received:', data);
        toast.error(`Kelime reddedildi: ${data.reason}`);
      };

      const gameEndedHandler = (data) => {
        console.log('🏁 [GamePage] Game ended event received:', data);
        setGameState('finished');
        toast.success('🏁 Oyun bitti!');
        loadGame();
      };

      const playerJoinedHandler = (data) => {
        console.log('👋 [GamePage] Player joined event received:', data);
        console.log('👤 Current user ID:', user?.id, 'Event user ID:', data.userId);
        if (data.userId !== user?.id) {
          toast.success(`👋 ${data.username} oyuna katıldı!`);
          loadGame();
        }
      };

      const playerLeftHandler = (data) => {
        console.log('👋 [GamePage] Player left event received:', data);
        toast.info(`👋 ${data.username} oyundan ayrıldı`);
        loadGame();
      };

      const playerTypingHandler = (data) => {
        console.log('⌨️ [GamePage] Player typing event received:', data);
        if (data.userId !== user?.id) {
          setTypingPlayers(prev => {
            if (data.isTyping) {
              return [...prev.filter(p => p.userId !== data.userId), data];
            } else {
              return prev.filter(p => p.userId !== data.userId);
            }
          });
        }
      };

      const errorHandler = (message) => {
        console.error('🚨 [GamePage] SignalR error:', message);
        toast.error(`SignalR Hatası: ${message}`);
      };

      // Store handlers in ref
      handlersRef.current = {
        GameStarted: gameStartedHandler,
        WordSubmitted: wordSubmittedHandler,
        WordRejected: wordRejectedHandler,
        GameEnded: gameEndedHandler,
        PlayerJoined: playerJoinedHandler,
        PlayerLeft: playerLeftHandler,
        PlayerTyping: playerTypingHandler,
        Error: errorHandler
      };

      // Register handlers DIRECTLY to SignalR connection
      const connection = signalRService.connection;
      if (connection) {
        connection.on('GameStarted', gameStartedHandler);
        connection.on('WordSubmitted', wordSubmittedHandler);
        connection.on('WordRejected', wordRejectedHandler);
        connection.on('GameEnded', gameEndedHandler);

        connection.off('PlayerJoined', playerJoinedHandler); 
        connection.on('PlayerJoined', playerJoinedHandler);
        
        connection.on('PlayerLeft', playerLeftHandler);
        connection.on('PlayerTyping', playerTypingHandler);
        connection.on('Error', errorHandler);

        console.log('✅ Handlers registered directly to SignalR connection');
      }

      console.log('✅ SignalR setup complete');
    } catch (error) {
      console.error('❌ SignalR setup error:', error);
      //toast.error('SignalR bağlantısı kurulamadı');
    }
  };

  const cleanupSignalR = () => {
    console.log('🧹 Cleaning up SignalR...');

    // Remove handlers DIRECTLY from SignalR connection
    const connection = signalRService.connection;
    if (connection && handlersRef.current) {
      try {
        // Remove specific handlers with exact function references
        connection.off('GameStarted', handlersRef.current.GameStarted);
        connection.off('WordSubmitted', handlersRef.current.WordSubmitted);
        connection.off('WordRejected', handlersRef.current.WordRejected);
        connection.off('GameEnded', handlersRef.current.GameEnded);
        connection.off('PlayerJoined', handlersRef.current.PlayerJoined);
        connection.off('PlayerLeft', handlersRef.current.PlayerLeft);
        connection.off('PlayerTyping', handlersRef.current.PlayerTyping);
        connection.off('Error', handlersRef.current.Error);

        console.log('✅ Specific handlers removed from SignalR connection');
      } catch (error) {
        console.error('Error removing specific handlers:', error);
      }
    }

    // Fallback: Remove ALL handlers for these events (aggressive cleanup)
    if (connection) {
      try {
        const events = ['GameStarted', 'WordSubmitted', 'WordRejected', 'GameEnded',
                       'PlayerJoined', 'PlayerLeft', 'PlayerTyping', 'Error'];

        events.forEach(event => {
          connection.off(event);
        });

        console.log('✅ All event handlers cleared as fallback');
      } catch (error) {
        console.error('Error in fallback cleanup:', error);
      }
    }

    handlersRef.current = null;
  };

  // Handle typing indicator
  const handleInputChange = (e) => {
    const value = e.target.value;
    setCurrentWord(value);

    // Send typing indicator
    if (value.length > 0 && !isTyping) {
      setIsTyping(true);
      signalRService.sendTypingIndicator(roomCode, true);
    } else if (value.length === 0 && isTyping) {
      setIsTyping(false);
      signalRService.sendTypingIndicator(roomCode, false);
    }
  };

  const handleWordSubmit = async (e) => {
    e.preventDefault();
    if (!currentWord.trim()) return;

    // Stop typing indicator
    if (isTyping) {
      setIsTyping(false);
      signalRService.sendTypingIndicator(roomCode, false);
    }

    if (requiredStartLetter && currentWord.trim().toLowerCase().charAt(0) !== requiredStartLetter.toLowerCase()) {
      toast.error(`Kelime '${requiredStartLetter.toUpperCase()}' harfi ile başlamalıdır!`);
      return;
    }

    try {
      const response = await gameAPI.submitWord(game.id, currentWord.trim());
      if (response.success) {
        setCurrentWord('');
        toast.success(`✅ Kelime kabul edildi! +${response.points} puan`);
        setRequiredStartLetter(response.requiredStartLetter);
        setCurrentPlayerTurn(response.nextPlayerTurnId);
        loadGame();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Kelime gönderilemedi';
      toast.error(errorMessage);
    }
  };

  const handleStartGame = async () => {
    try {
      const response = await gameAPI.startGame(game.id);
      if (response.success) {
        toast.success('Oyun başlatıldı!');
        setGameState('playing');
        loadGame();
      } else {
        toast.error('Oyun başlatılamadı');
      }
    } catch (error) {
      toast.error('Oyun başlatılamadı');
    }
  };

  const handleLeaveGame = async () => {
    try {
      await gameAPI.leaveGame(game.id);
      navigate('/');
      toast.success('Oyundan ayrıldınız');
    } catch (error) {
      toast.error('Oyundan ayrılırken hata oluştu');
    }
  };

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Oyun yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!game) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Oyun Bulunamadı</h1>
          <button
            onClick={() => navigate('/')}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700"
          >
            Ana Sayfaya Dön
          </button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 p-2 sm:p-4"
    >
      <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6">
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg shadow-lg p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold mb-2">🎮 Kelime Zinciri</h1>
              <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-purple-100">
                <span className="bg-white/20 px-3 py-1 rounded-full text-sm font-medium">
                  📍 Oda: {roomCode}
                </span>
                <span className="bg-white/20 px-3 py-1 rounded-full text-sm font-medium">
                  👥 {game?.currentPlayerCount || 0}/{game?.maxPlayers || 4} Oyuncu
                </span>
                {timeRemaining > 0 && gameState === 'playing' && (
                  <span className={`bg-white/20 px-3 py-1 rounded-full text-sm font-medium ${
                    timeRemaining < 60000 ? 'animate-pulse bg-red-500/30' : ''
                  }`}>
                    ⏰ {Math.floor(timeRemaining / 60000)}:{String(Math.floor((timeRemaining % 60000) / 1000)).padStart(2, '0')}
                  </span>
                )}
              </div>
            </div>
            <motion.button
              onClick={handleLeaveGame}
              className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              🚪 Oyundan Ayrıl
            </motion.button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-800">🏆 Oyuncular</h2>
          <div className="space-y-3">
            {game.players?.map((player) => (
              <div
                key={player.user.id}
                className={`flex items-center space-x-4 p-4 rounded-lg ${
                  player.user.id === user?.id
                    ? 'bg-gradient-to-r from-purple-100 to-blue-100 border-2 border-purple-300'
                    : 'bg-gray-50'
                } ${
                  currentPlayerTurn === player.user.id ? 'ring-2 ring-yellow-400' : ''
                }`}
              >
                <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg ${
                  player.user.id === user?.id ? 'bg-purple-600' : 'bg-blue-600'
                }`}>
                  {player.user.username?.charAt(0).toUpperCase()}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-900">{player.user.username}</span>
                    {player.user.id === user?.id && (
                      <span className="text-xs bg-purple-500 text-white px-2 py-1 rounded-full">Sen</span>
                    )}
                    {currentPlayerTurn === player.user.id && (
                      <span className="text-xs bg-yellow-500 text-white px-2 py-1 rounded-full animate-pulse">
                        🎯 Sırada
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600">{player.score || 0}</div>
                  <div className="text-xs text-gray-500">puan</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Game Stats */}
        {gameState === 'playing' && gameStats.totalWords > 0 && (
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">📊 Oyun İstatistikleri</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{gameStats.totalWords}</div>
                <div className="text-xs text-gray-600">Toplam Kelime</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{gameStats.averagePoints}</div>
                <div className="text-xs text-gray-600">Ortalama Puan</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-lg font-bold text-purple-600">{gameStats.longestWord}</div>
                <div className="text-xs text-gray-600">En Uzun Kelime</div>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <div className="text-lg font-bold text-orange-600">{gameStats.fastestWord}</div>
                <div className="text-xs text-gray-600">İlk Kelime</div>
              </div>
            </div>
          </div>
        )}

        {gameState === 'waiting' && (
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">Oyun Başlamayı Bekliyor</h2>
            <p className="text-gray-600 mb-6">
              {game.isSinglePlayer
                ? 'Tek oyunculu oyunu başlatmak için butona tıklayın.'
                : (game.createdByUser?.id === user?.id)
                  ? 'Oyunu başlatmak için butona tıklayın.'
                  : 'Oyun sahibinin oyunu başlatmasını bekleyin.'
              }
            </p>
            {(game.createdByUser?.id === user?.id) && (
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleStartGame}
                className="bg-primary-600 text-white px-8 py-3 rounded-lg hover:bg-primary-700 font-semibold"
              >
                🎮 Oyunu Başlat
              </motion.button>
            )}
          </div>
        )}

        {gameState === 'playing' && (
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">🎮 Kelime Gir</h2>
              {requiredStartLetter && (
                <p className="text-lg text-gray-600">
                  <span className="font-semibold text-purple-600">'{requiredStartLetter.toUpperCase()}'</span>
                  {' '}harfi ile başlayan bir kelime yazın
                </p>
              )}
              {currentPlayerTurn && currentPlayerTurn !== user?.id && (
                <div className="mt-2 p-2 bg-yellow-100 rounded-lg">
                  <p className="text-yellow-800">🕐 Sıra başka oyuncuda... Bekleyin</p>
                </div>
              )}
              {typingPlayers.length > 0 && (
                <div className="mt-2 p-2 bg-blue-100 rounded-lg">
                  <p className="text-blue-800">
                    ⌨️ {typingPlayers.map(p => p.username).join(', ')} yazıyor...
                  </p>
                </div>
              )}
            </div>

            <form onSubmit={handleWordSubmit} className="space-y-4">
              <div className="relative">
                <input
                  type="text"
                  value={currentWord}
                  onChange={handleInputChange}
                  placeholder={requiredStartLetter ? `${requiredStartLetter.toUpperCase()}...` : "Kelimenizi girin..."}
                  className={`w-full px-6 py-4 text-xl border-2 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 ${
                    currentPlayerTurn && currentPlayerTurn !== user?.id
                      ? 'border-gray-200 bg-gray-50'
                      : 'border-gray-300 bg-white'
                  } ${
                    requiredStartLetter && currentWord.length > 0 &&
                    currentWord.toLowerCase().charAt(0) !== requiredStartLetter.toLowerCase()
                      ? 'border-red-400 bg-red-50'
                      : ''
                  }`}
                  autoFocus
                  disabled={currentPlayerTurn && currentPlayerTurn !== user?.id}
                />
                {currentWord.length > 0 && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
                    {currentWord.length} harf
                  </div>
                )}
              </div>
              <motion.button
                type="submit"
                disabled={!currentWord.trim() || (currentPlayerTurn && currentPlayerTurn !== user?.id)}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-4 rounded-xl hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-semibold text-lg"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                🚀 Kelime Gönder
              </motion.button>
            </form>
          </div>
        )}

        {/* Recent Moves */}
        {gameState === 'playing' && recentMoves.length > 0 && (
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">📝 Son Kelimeler</h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {recentMoves.slice(0, 10).map((move, index) => (
                <motion.div
                  key={move.id || index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      {move.user?.username?.charAt(0).toUpperCase() || move.username?.charAt(0).toUpperCase() || '?'}
                    </div>
                    <div>
                      <span className="font-medium text-gray-800">{move.word}</span>
                      <div className="text-xs text-gray-500">
                        {move.user?.username || move.username || 'Bilinmeyen'}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600 font-semibold">+{move.points || 0}</span>
                    {move.createdAt && (
                      <span className="text-xs text-gray-400">
                        {new Date(move.createdAt).toLocaleTimeString('tr-TR', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {gameState === 'finished' && (
          <motion.div
            className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg shadow-lg p-8 text-center border-2 border-yellow-200"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="mb-6">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-3xl font-bold text-gray-800 mb-2">Oyun Bitti!</h2>
            </div>

            {game?.players && game.players.length > 0 && (
              <div className="mb-6">
                <div className="text-2xl font-bold text-yellow-600 mb-2">🏆 Kazanan</div>
                <div className="bg-white rounded-lg p-4 inline-block shadow-md">
                  <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-2xl mx-auto mb-2">
                    {game.players.sort((a, b) => (b.score || 0) - (a.score || 0))[0]?.user.username?.charAt(0).toUpperCase()}
                  </div>
                  <div className="font-bold text-xl text-gray-800">
                    {game.players.sort((a, b) => (b.score || 0) - (a.score || 0))[0]?.user.username}
                  </div>
                  <div className="text-2xl font-bold text-green-600">
                    {game.players.sort((a, b) => (b.score || 0) - (a.score || 0))[0]?.score || 0} puan
                  </div>
                </div>
              </div>
            )}

            <motion.button
              onClick={() => navigate('/')}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              🏠 Ana Sayfaya Dön
            </motion.button>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default GamePage;