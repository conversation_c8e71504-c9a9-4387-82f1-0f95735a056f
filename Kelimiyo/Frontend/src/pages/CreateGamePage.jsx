import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  ArrowLeft, 
  Users, 
  Clock, 
  Target, 
  Zap,
  User,
  UserPlus
} from 'lucide-react';
import { gameAPI } from '../services/api';

const CreateGamePage = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [gameSettings, setGameSettings] = useState({
    isSinglePlayer: false,
    difficulty: 2, // 1=Easy, 2=Medium, 3=Hard
    maxPlayers: 4,
    timeLimit: 30,
    maxRounds: 10
  });

  const difficultyOptions = [
    { value: 1, label: 'Kolay', description: 'Basit kelimeler' },
    { value: 2, label: 'Orta', description: 'Orta seviye kelimeler' },
    { value: 3, label: 'Zor', description: 'Zor<PERSON> kelimeler' }
  ];

  const playerCountOptions = [2, 3, 4, 5, 6, 7, 8];
  const timeLimitOptions = [15, 30, 45, 60, 90, 120];
  const roundOptions = [5, 10, 15, 20];

  const handleCreateGame = async () => {
    try {
      setIsLoading(true);
      
      const gameData = {
        difficulty: gameSettings.difficulty,
        maxPlayers: gameSettings.isSinglePlayer ? 1 : gameSettings.maxPlayers,
        timeLimit: gameSettings.timeLimit,
        maxRounds: gameSettings.maxRounds,
        isSinglePlayer: gameSettings.isSinglePlayer
      };

      const response = await gameAPI.createRoom(gameData);

      if (response.success) {
        const roomCode = response.game.roomCode;
        navigate(`/game/${roomCode}`);
        toast.success('Oyun odası oluşturuldu!');
      } else {
        toast.error('Oyun oluşturulamadı: ' + (response.message || 'Bilinmeyen hata'));
      }
    } catch (error) {
      console.error('Create game error:', error);
      toast.error('Oyun oluşturulurken hata oluştu: ' + (error.response?.data?.message || error.message));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center mb-8"
        >
          <button
            onClick={() => navigate('/')}
            className="btn-secondary mr-4"
          >
            <ArrowLeft size={20} />
            Geri
          </button>
          <h1 className="text-3xl font-bold text-gray-800">Yeni Oyun Oluştur</h1>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Game Mode Selection */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div className="card p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center text-white">
                <Users className="mr-2 text-white" size={20} />
                Oyun Modu
              </h2>
              
              <div className="grid grid-cols-2 gap-4">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setGameSettings(prev => ({ ...prev, isSinglePlayer: false }))}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    !gameSettings.isSinglePlayer
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <UserPlus className="mx-auto mb-2" size={24} />
                  <div className="font-medium">Çok Oyunculu</div>
                  <div className={`text-sm ${!gameSettings.isSinglePlayer ? 'text-primary-600' : 'text-gray-500'}`}>Arkadaşlarınla oyna</div>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setGameSettings(prev => ({ ...prev, isSinglePlayer: true }))}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    gameSettings.isSinglePlayer
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <User className="mx-auto mb-2" size={24} />
                  <div className="font-medium">Tek Oyunculu</div>
                  <div className={`text-sm ${gameSettings.isSinglePlayer ? 'text-primary-600' : 'text-gray-500'}`}>Kendi başına oyna</div>
                </motion.button>
              </div>
            </div>

            {/* Difficulty Selection */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center text-white">
                <Target className="mr-2 text-white" size={20} />
                Zorluk Seviyesi
              </h2>
              
              <div className="space-y-3">
                {difficultyOptions.map((option) => (
                  <motion.button
                    key={option.value}
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                    onClick={() => setGameSettings(prev => ({ ...prev, difficulty: option.value }))}
                    className={`w-full p-3 rounded-lg border-2 text-left transition-all ${
                      gameSettings.difficulty === option.value
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-200 hover:border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <div className="font-medium">{option.label}</div>
                    <div className={`text-sm ${gameSettings.difficulty === option.value ? 'text-primary-600' : 'text-gray-500'}`}>{option.description}</div>
                  </motion.button>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Game Settings */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            {/* Player Count (only for multiplayer) */}
            {!gameSettings.isSinglePlayer && (
              <div className="card p-6">
                <h2 className="text-xl font-semibold mb-4 flex items-center text-white">
                  <Users className="mr-2 text-white" size={20} />
                  Oyuncu Sayısı
                </h2>
                
                <div className="grid grid-cols-4 gap-2">
                  {playerCountOptions.map((count) => (
                    <motion.button
                      key={count}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setGameSettings(prev => ({ ...prev, maxPlayers: count }))}
                      className={`p-3 rounded-lg border-2 font-medium transition-all ${
                        gameSettings.maxPlayers === count
                          ? 'border-primary-500 bg-primary-500 text-white'
                          : 'border-gray-200 hover:border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {count}
                    </motion.button>
                  ))}
                </div>
              </div>
            )}

            {/* Time Limit */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center text-white">
                <Clock className="mr-2 text-white" size={20} />
                Süre Limiti (saniye)
              </h2>
              
              <div className="grid grid-cols-3 gap-2">
                {timeLimitOptions.map((time) => (
                  <motion.button
                    key={time}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setGameSettings(prev => ({ ...prev, timeLimit: time }))}
                    className={`p-3 rounded-lg border-2 font-medium transition-all ${
                      gameSettings.timeLimit === time
                        ? 'border-primary-500 bg-primary-500 text-white'
                        : 'border-gray-200 hover:border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {time}s
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Round Count */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center text-white">
                <Zap className="mr-2 text-white" size={20} />
                Round Sayısı
              </h2>
              
              <div className="grid grid-cols-4 gap-2">
                {roundOptions.map((rounds) => (
                  <motion.button
                    key={rounds}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setGameSettings(prev => ({ ...prev, maxRounds: rounds }))}
                    className={`p-3 rounded-lg border-2 font-medium transition-all ${
                      gameSettings.maxRounds === rounds
                        ? 'border-primary-500 bg-primary-500 text-white'
                        : 'border-gray-200 hover:border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {rounds}
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Create Button */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleCreateGame}
              disabled={isLoading}
              className="w-full btn-primary py-4 text-lg font-semibold"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Oluşturuluyor...
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <Zap className="mr-2" size={20} />
                  Oyunu Oluştur
                </div>
              )}
            </motion.button>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default CreateGamePage;
