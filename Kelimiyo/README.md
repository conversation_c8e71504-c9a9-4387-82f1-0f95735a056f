# 🎯 Kelime Zinciri - Türkçe Kelime Oyunu

Modern, gerçek zamanlı çok oyunculu Türkçe kelime zinciri oyunu. .NET 8.0 Core API backend ve React 18 frontend ile geliştirilmiştir.

## 🚀 Özellikler

### 🎮 Oyun Özellikleri
- **3 Zorluk Seviyesi**: Kolay, Orta, Zor
- **Gerçek Zamanlı Multiplayer**: SignalR ile anlık oyun deneyimi
- **Türkçe Kelime Doğrulama**: 40,000+ Türkçe kelime veritabanı
- **Dinamik Puanlama Sistemi**: <PERSON><PERSON><PERSON> uzunluğu, zorluk ve hıza göre puanlama
- **Zamanlayıcı Sistemi**: Oyuncu başına süre limiti

### 👤 Kullanıcı Sistemi
- **JWT Authentication**: Güvenli kimlik doğrulama
- **Profil Yönetimi**: Kullanıcı bilgileri ve avatar
- **İstatistik Takibi**: Detaylı oyun istatistikleri
- **Başarım Rozeti**: Çeşitli başarımlar ve rozetler
- **Arkadaş Sistemi**: Arkadaş ekleme ve davet etme

### 🏆 Sosyal Özellikler
- **Global Liderboard**: Tüm oyuncular arası sıralama
- **Günlük/Haftalık Sıralama**: Periyodik liderlik tabloları
- **Turnuva Sistemi**: Özel turnuvalar
- **Paylaşım Özellikleri**: Sosyal medya entegrasyonu
- **Clan Sistemi**: Takım oyunu

### 📱 Modern UI/UX
- **Responsive Tasarım**: Tüm cihazlarda uyumlu
- **5 Farklı Tema**: Kişiselleştirilebilir arayüz
- **Smooth Animasyonlar**: Framer Motion ile akıcı geçişler
- **PWA Support**: Progressive Web App desteği
- **Offline Mode**: Çevrimdışı oyun modu

## 🛠️ Teknoloji Stack

### Backend
- **.NET 9.0 Core API**: En güncel web API framework
- **Entity Framework Core**: ORM ve veritabanı yönetimi
- **SignalR**: Gerçek zamanlı iletişim
- **PostgreSQL**: Ana veritabanı (açık kaynak, performanslı)
- **Redis**: Cache ve session yönetimi
- **JWT Authentication**: Güvenli kimlik doğrulama

### Frontend
- **React 18**: Modern UI library
- **Vite**: Hızlı build tool
- **Tailwind CSS**: Utility-first CSS framework
- **Framer Motion**: Animasyon library
- **Axios**: HTTP client
- **React Query**: Server state yönetimi
- **SignalR Client**: Gerçek zamanlı bağlantı

### DevOps
- **Docker**: Containerization
- **Docker Compose**: Multi-container orchestration
- **Nginx**: Reverse proxy ve static file serving
- **GitHub Actions**: CI/CD pipeline

## 📁 Proje Yapısı

```
KelimeZinciri/
├── Backend/
│   ├── KelimeZinciri.API/
│   │   ├── Controllers/          # API Controllers
│   │   ├── Models/              # Data models
│   │   ├── Services/            # Business logic
│   │   ├── Data/                # Database context
│   │   ├── Hubs/                # SignalR hubs
│   │   └── Program.cs           # Application entry point
│   └── Data/
│       └── turkce_kelimeler.json # Turkish words database
├── Frontend/
│   ├── public/                  # Static files
│   ├── src/
│   │   ├── components/          # React components
│   │   ├── pages/              # Page components
│   │   ├── services/           # API services
│   │   ├── contexts/           # React contexts
│   │   └── styles/             # CSS styles
│   ├── package.json
│   └── vite.config.js
├── Database/
│   ├── init.sql                # Database initialization
│   └── kelimeler.sql           # Words data
├── Docker/
│   ├── Dockerfile.backend      # Backend container
│   ├── Dockerfile.frontend     # Frontend container
│   └── docker-compose.yml      # Multi-container setup
└── README.md
```

## 🚀 Kurulum ve Çalıştırma

### Ön Gereksinimler
- **.NET 9.0 SDK**
- **Node.js 18+**
- **PostgreSQL 14+** (açık kaynak veritabanı)
- **Redis** (opsiyonel, cache için)
- **Docker** (containerized deployment için)

### 1. Repository'yi Klonlayın
```bash
git clone https://github.com/yourusername/kelime-zinciri.git
cd kelime-zinciri
```

### 2. PostgreSQL Kurulumu
```bash
# Windows için PostgreSQL indirin:
# https://www.postgresql.org/download/windows/

# macOS için Homebrew ile:
brew install postgresql
brew services start postgresql

# Ubuntu/Debian için:
sudo apt update
sudo apt install postgresql postgresql-contrib

# PostgreSQL'e bağlanın ve veritabanı oluşturun:
sudo -u postgres psql
CREATE DATABASE "KelimeZinciriDB";
CREATE USER kelimiyo WITH PASSWORD '********';
GRANT ALL PRIVILEGES ON DATABASE "KelimeZinciriDB" TO kelimiyo;
\q
```

### 3. Backend Kurulumu
```bash
cd Backend/KelimeZinciri.API

# Paketleri yükleyin
dotnet restore

# Veritabanını oluşturun ve migration'ları uygulayın
dotnet ef database update

# Uygulamayı çalıştırın
dotnet run
```

Backend https://localhost:7001 adresinde çalışacaktır.

### 4. Frontend Kurulumu
```bash
cd Frontend

# Paketleri yükleyin
npm install

# Development server'ı başlatın
npm run dev
```

Frontend http://localhost:3000 adresinde çalışacaktır.

### 5. Docker ile Çalıştırma
```bash
# Tüm servisleri başlatın
docker-compose -f Docker/docker-compose.yml up -d

# Logları takip edin
docker-compose -f Docker/docker-compose.yml logs -f
```

## 🔧 Konfigürasyon

### Backend Konfigürasyonu (appsettings.json)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=KelimeZinciriDB;Username=kelimiyo;Password=********;Include Error Detail=true"
  },
  "Jwt": {
    "Key": "your-secret-key",
    "Issuer": "KelimeZinciri.API",
    "Audience": "KelimeZinciri.Client"
  },
  "Redis": {
    "ConnectionString": "localhost:6379"
  }
}
```

### Frontend Konfigürasyonu (.env)
```env
VITE_API_URL=https://localhost:7001/api
VITE_SIGNALR_URL=https://localhost:7001/gameHub
```

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/login` - Kullanıcı girişi
- `POST /api/auth/register` - Kullanıcı kaydı
- `POST /api/auth/validate-token` - Token doğrulama

### Game
- `POST /api/game/create-room` - Oyun odası oluştur
- `POST /api/game/join-room/{roomCode}` - Oyuna katıl
- `GET /api/game/validate-word/{word}` - Kelime doğrula
- `POST /api/game/{gameId}/submit-word` - Kelime gönder
- `GET /api/game/active` - Aktif oyunlar

### Statistics & Leaderboard
- `GET /api/statistics/user/{userId}` - Kullanıcı istatistikleri
- `GET /api/leaderboard/global` - Global sıralama
- `GET /api/leaderboard/weekly` - Haftalık sıralama

## 🎮 Oyun Kuralları

1. **Kelime Zinciri**: Her kelime bir önceki kelimenin son harfi ile başlamalıdır
2. **Geçerli Kelimeler**: Sadece Türkçe sözlükte bulunan kelimeler kabul edilir
3. **Tekrar Yasağı**: Aynı oyunda kullanılan kelimeler tekrar kullanılamaz
4. **Süre Limiti**: Her oyuncu için belirli süre limiti vardır
5. **Puanlama**: Kelime uzunluğu, zorluk ve hıza göre puan hesaplanır

## 🏗️ Geliştirme

### Backend Geliştirme
```bash
# Test çalıştırma
dotnet test

# Migration oluşturma
dotnet ef migrations add MigrationName

# Veritabanını güncelleme
dotnet ef database update
```

### Frontend Geliştirme
```bash
# Linting
npm run lint

# Build
npm run build

# Preview build
npm run preview
```

## 🚀 Deployment

### Production Build
```bash
# Backend
dotnet publish -c Release -o ./publish

# Frontend
npm run build
```

### Docker Production
```bash
docker-compose -f Docker/docker-compose.yml --profile production up -d
```

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için [LICENSE](LICENSE) dosyasına bakın.

## 👥 Takım

- **Backend Developer**: .NET Core API, SignalR, Database
- **Frontend Developer**: React, UI/UX, Animations
- **DevOps Engineer**: Docker, CI/CD, Deployment

## 📞 İletişim

- **Email**: <EMAIL>
- **Website**: https://kelimezinciri.com
- **GitHub**: https://github.com/yourusername/kelime-zinciri

## 🎯 Roadmap

### v2.0 Planları
- [ ] **Mobil Uygulama**: React Native ile iOS ve Android
- [ ] **AI Bot Rakip**: Farklı seviyelerde AI rakip
- [ ] **Çok Dilli Destek**: İngilizce, Almanca, Fransızca
- [ ] **Monetizasyon**: Premium üyelik sistemi
- [ ] **Turnuva Sistemi**: Düzenli turnuvalar
- [ ] **Sesli Chat**: Oyun içi sesli iletişim

---

⭐ **Projeyi beğendiyseniz yıldız vermeyi unutmayın!**
