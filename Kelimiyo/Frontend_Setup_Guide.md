# 🎮 <PERSON><PERSON>e Zinciri Frontend Kurulum Rehberi

Bu rehber, <PERSON><PERSON><PERSON> Zinciri frontend uygulamasını makinenizde çalıştırmanız için gerekli adımları içerir.

## 📋 <PERSON><PERSON> Gereksinimler

### 1. Node.js Kurulumu
- **Node.js 18+** gereklidir
- [Node.js resmi sitesinden](https://nodejs.org/) indirin
- LTS (Long Term Support) sürümünü tercih edin

### 2. <PERSON><PERSON><PERSON> Kontrolü
```bash
# Node.js versiyonunu kontrol edin
node --version

# npm versiyonunu kontrol edin
npm --version
```

## 🚀 Hızlı Başlangıç

### Otomatik Kurulum (Önerilen)
```bash
# Proje kök dizininde
./start_frontend.bat
```

Bu script otomatik olarak:
- Node.js varlığını kontrol eder
- Gerekli paketleri yükler
- Frontend'i başlatır

### <PERSON>

#### 1. Frontend Dizinine Geçin
```bash
cd Frontend
```

#### 2. Paketleri Yü<PERSON>in
```bash
# npm ile
npm install

# veya yarn ile (eğer kuruluysa)
yarn install
```

#### 3. Environment Variables Kontrol Edin
`.env` dosyasının mevcut olduğundan emin olun:
```env
VITE_API_URL=https://localhost:7001/api
VITE_SIGNALR_URL=https://localhost:7001/gameHub
```

#### 4. Frontend'i Başlatın
```bash
# Development mode
npm run dev

# veya yarn ile
yarn dev
```

## 🌐 Erişim Bilgileri

### Frontend
- **URL:** http://localhost:3000
- **Port:** 3000

### Backend Proxy
- **API:** https://localhost:7001/api
- **SignalR:** https://localhost:7001/gameHub

## 📁 Proje Yapısı

```
Frontend/
├── public/                 # Static dosyalar
│   ├── index.html         # Ana HTML dosyası
│   └── manifest.json      # PWA manifest
├── src/                   # Kaynak kodlar
│   ├── components/        # React bileşenleri
│   │   ├── Layout/       # Layout bileşenleri
│   │   └── UI/           # UI bileşenleri
│   ├── contexts/         # React Context'ler
│   ├── pages/            # Sayfa bileşenleri
│   ├── services/         # API servisleri
│   │   ├── api.js        # Ana API servisi
│   │   └── signalr.js    # SignalR servisi
│   ├── styles/           # CSS dosyaları
│   ├── App.jsx           # Ana uygulama bileşeni
│   └── main.jsx          # Giriş noktası
├── package.json          # Proje bağımlılıkları
├── vite.config.js        # Vite konfigürasyonu
├── tailwind.config.js    # Tailwind CSS konfigürasyonu
└── .env                  # Environment variables
```

## 🛠️ Geliştirme Komutları

```bash
# Development server başlat
npm run dev

# Production build oluştur
npm run build

# Build'i preview et
npm run preview

# Linting çalıştır
npm run lint
```

## 🔧 Konfigürasyon

### Vite Konfigürasyonu
`vite.config.js` dosyasında:
- Port: 3000
- Backend proxy ayarları
- Build konfigürasyonu

### Tailwind CSS
`tailwind.config.js` dosyasında:
- Custom renkler
- Responsive breakpoint'ler
- Custom utility'ler

### Environment Variables
`.env` dosyasında:
- API URL'leri
- Uygulama ayarları
- Geliştirme ayarları

## 🎯 Özellikler

### Teknolojiler
- **React 18** - Modern React hooks ve features
- **Vite** - Hızlı build tool
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animasyonlar
- **React Query** - Server state management
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **SignalR** - Real-time communication

### UI Bileşenleri
- Responsive tasarım
- Dark/Light theme desteği
- Toast notifications
- Loading states
- Error boundaries

### Game Features
- Real-time multiplayer
- Room creation/joining
- Word validation
- Score tracking
- Leaderboards
- User profiles

## 🐛 Sorun Giderme

### Yaygın Hatalar

#### 1. "node: command not found"
```bash
# Node.js'i yükleyin
# Windows: https://nodejs.org/
# macOS: brew install node
# Ubuntu: sudo apt install nodejs npm
```

#### 2. "npm install" Hataları
```bash
# Cache'i temizleyin
npm cache clean --force

# node_modules'ü silin ve tekrar yükleyin
rm -rf node_modules package-lock.json
npm install
```

#### 3. Port 3000 Kullanımda
```bash
# Farklı port kullanın
npm run dev -- --port 3001
```

#### 4. Backend Bağlantı Hatası
- Backend'in çalıştığından emin olun (https://localhost:7001)
- SSL sertifika hatalarını göz ardı edin (development)
- Firewall ayarlarını kontrol edin

### Performance İpuçları

#### 1. Development
```bash
# Hot reload için
npm run dev

# Network üzerinden erişim için
npm run dev -- --host
```

#### 2. Production
```bash
# Optimize build
npm run build

# Build boyutunu analiz et
npm run build -- --analyze
```

## 📱 Mobile Support

### Responsive Design
- Mobile-first approach
- Touch-friendly interface
- Adaptive layouts

### PWA Features
- Offline support (gelecek)
- Install prompt
- Service worker

## 🔒 Güvenlik

### Authentication
- JWT token based
- Automatic token refresh
- Secure storage

### API Security
- HTTPS only
- CORS configuration
- Request/response interceptors

## 📊 Monitoring

### Development Tools
- React DevTools
- Redux DevTools (eğer kullanılıyorsa)
- Network monitoring

### Error Tracking
- Console error logging
- Toast notifications
- Error boundaries

## 🚀 Deployment

### Build Process
```bash
# Production build
npm run build

# Build dosyaları dist/ klasöründe
```

### Static Hosting
- Netlify
- Vercel
- GitHub Pages
- Firebase Hosting

## 📞 Destek

### Sorun Bildirimi
1. Console hatalarını kontrol edin
2. Network tab'ını inceleyin
3. Backend loglarını kontrol edin
4. GitHub Issues'da sorun bildirin

### Geliştirme
1. Fork yapın
2. Feature branch oluşturun
3. Değişiklikleri commit edin
4. Pull request gönderin

---

**Not:** Bu rehber sürekli güncellenmektedir. En son sürüm için GitHub repository'sini kontrol edin.
