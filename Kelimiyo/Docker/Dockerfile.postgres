# PostgreSQL with Turkish locale support for Kelime Zinciri
FROM postgres:16-alpine

# Install additional packages for Turkish locale support
RUN apk add --no-cache \
    icu-data-full \
    icu-libs

# Set environment variables
ENV POSTGRES_DB=KelimeZinciriDB
ENV POSTGRES_USER=postgres
ENV POSTGRES_PASSWORD=KelimeZinciri123!
ENV POSTGRES_INITDB_ARGS="--encoding=UTF-8 --lc-collate=tr_TR.UTF-8 --lc-ctype=tr_TR.UTF-8"

# Copy initialization scripts
COPY ./Database/init_postgresql.sql /docker-entrypoint-initdb.d/01-init.sql

# Create custom postgresql.conf for optimization
RUN echo "# PostgreSQL configuration for Kelime Zinciri" > /tmp/postgresql.conf.custom && \
    echo "# Memory settings" >> /tmp/postgresql.conf.custom && \
    echo "shared_buffers = 256MB" >> /tmp/postgresql.conf.custom && \
    echo "effective_cache_size = 1GB" >> /tmp/postgresql.conf.custom && \
    echo "work_mem = 4MB" >> /tmp/postgresql.conf.custom && \
    echo "maintenance_work_mem = 64MB" >> /tmp/postgresql.conf.custom && \
    echo "" >> /tmp/postgresql.conf.custom && \
    echo "# Connection settings" >> /tmp/postgresql.conf.custom && \
    echo "max_connections = 100" >> /tmp/postgresql.conf.custom && \
    echo "" >> /tmp/postgresql.conf.custom && \
    echo "# Logging settings" >> /tmp/postgresql.conf.custom && \
    echo "log_statement = 'mod'" >> /tmp/postgresql.conf.custom && \
    echo "log_min_duration_statement = 1000" >> /tmp/postgresql.conf.custom && \
    echo "" >> /tmp/postgresql.conf.custom && \
    echo "# Performance settings" >> /tmp/postgresql.conf.custom && \
    echo "random_page_cost = 1.1" >> /tmp/postgresql.conf.custom && \
    echo "effective_io_concurrency = 200" >> /tmp/postgresql.conf.custom && \
    echo "" >> /tmp/postgresql.conf.custom && \
    echo "# Turkish locale settings" >> /tmp/postgresql.conf.custom && \
    echo "lc_messages = 'tr_TR.UTF-8'" >> /tmp/postgresql.conf.custom && \
    echo "lc_monetary = 'tr_TR.UTF-8'" >> /tmp/postgresql.conf.custom && \
    echo "lc_numeric = 'tr_TR.UTF-8'" >> /tmp/postgresql.conf.custom && \
    echo "lc_time = 'tr_TR.UTF-8'" >> /tmp/postgresql.conf.custom

# Copy the custom configuration
COPY --from=0 /tmp/postgresql.conf.custom /etc/postgresql/postgresql.conf.custom

# Expose PostgreSQL port
EXPOSE 5432

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD pg_isready -U $POSTGRES_USER -d $POSTGRES_DB || exit 1

# Add labels for better container management
LABEL maintainer="Kelime Zinciri Team"
LABEL description="PostgreSQL database for Kelime Zinciri game"
LABEL version="1.0"
