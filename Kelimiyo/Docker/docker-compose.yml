version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: kelime-zinciri-db
    environment:
      - POSTGRES_DB=KelimeZinciriDB
      - POSTGRES_USER=kelimiyo
      - POSTGRES_PASSWORD=********
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=tr_TR.UTF-8 --lc-ctype=tr_TR.UTF-8
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./Database/init_postgresql.sql:/docker-entrypoint-initdb.d/01-init.sql
    networks:
      - kelime-zinciri-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U kelimiyo -d KelimeZinciriDB"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: kelime-zinciri-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - kelime-zinciri-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Backend API
  backend:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.backend
    container_name: kelime-zinciri-backend
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=KelimeZinciriDB;Username=kelimiyo;Password=********;Include Error Detail=true
      - Redis__ConnectionString=redis:6379
      - Jwt__Key=KelimeZinciri_Super_Secret_Key_2024_Very_Long_And_Secure_Key_For_JWT_Token_Generation
      - Jwt__Issuer=KelimeZinciri.API
      - Jwt__Audience=KelimeZinciri.Client
    ports:
      - "5000:80"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - kelime-zinciri-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Frontend
  frontend:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.frontend
      args:
        - VITE_API_URL=http://localhost:5000/api
    container_name: kelime-zinciri-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - kelime-zinciri-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: kelime-zinciri-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    networks:
      - kelime-zinciri-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  kelime-zinciri-network:
    driver: bridge
