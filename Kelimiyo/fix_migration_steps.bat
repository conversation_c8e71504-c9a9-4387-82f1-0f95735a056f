@echo off
echo ========================================
echo    PostgreSQL Migration Fix Script
echo ========================================
echo.

:: Set variables
set BACKEND_DIR=%~dp0Backend\KelimeZinciri.API

echo [1/6] PostgreSQL bağlantısı kontrol ediliyor...

:: Check if PostgreSQL is running
docker ps | findstr postgres >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ PostgreSQL Docker container çalışıyor
) else (
    echo PostgreSQL container bulunamadı. Başlatılıyor...
    docker run --name kelime-postgres -d ^
        -e POSTGRES_DB=KelimeZinciriDB ^
        -e POSTGRES_USER=kelimiyo ^
        -e POSTGRES_PASSWORD=admin123 ^
        -p 5432:5432 ^
        postgres:16-alpine

    echo PostgreSQL başlatıldı. 10 saniye bekleniyor...
    timeout /t 10 /nobreak >nul
)

echo.
echo [2/6] Backend dizinine geçiliyor...
cd /d "%BACKEND_DIR%"
if %errorLevel% neq 0 (
    echo HATA: Backend dizini bulunamadı: %BACKEND_DIR%
    pause
    exit /b 1
)

echo.
echo [3/6] Mevcut migration'lar temizleniyor...
if exist "Migrations" (
    echo Migrations klasörü siliniyor...
    rmdir /s /q Migrations
)

echo.
echo [4/6] Veritabanı sıfırlanıyor...
dotnet ef database drop --force
if %errorLevel% neq 0 (
    echo UYARI: Veritabanı silinemedi (normal olabilir)
)

echo.
echo [5/6] Yeni migration oluşturuluyor...
dotnet ef migrations add InitialPostgreSQLMigration
if %errorLevel% neq 0 (
    echo HATA: Migration oluşturulamadı
    pause
    exit /b 1
)

echo.
echo [6/6] Migration uygulanıyor...
dotnet ef database update
if %errorLevel% neq 0 (
    echo HATA: Migration uygulanamadı
    pause
    exit /b 1
)

echo.
echo ========================================
echo         Migration Tamamlandı!
echo ========================================
echo.
echo Şimdi uygulamayı başlatabilirsiniz:
echo dotnet run
echo.
echo Veya Docker Compose ile:
echo docker-compose -f ../Docker/docker-compose.yml up -d
echo.
pause

cd /d "%~dp0"
exit /b 0
