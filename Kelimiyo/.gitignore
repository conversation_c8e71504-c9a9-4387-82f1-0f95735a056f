# Kelime Zinciri - Git Ignore File

# ===========================
# .NET Core / ASP.NET Core
# ===========================

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio 2015/2017 cache/options directory
.vs/

# Visual Studio Code
.vscode/

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Mono auto generated files
mono_crash.*

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio 2015/2017 cache/options directory
.vs/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit
*.VisualState.xml
TestResult.xml
nunit-*.xml

# Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

# Benchmark Results
BenchmarkDotNet.Artifacts/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# StyleCop
StyleCopReport.xml

# Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Entity Framework
*.edmx.diagram
*.edmx.sql

# ===========================
# Node.js / React / Frontend
# ===========================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vite build output
dist/
dist-ssr/

# Rollup.js default build output
dist/

# Storybook build outputs
.out
.storybook-out

# ===========================
# Database
# ===========================

# SQL Server files
*.mdf
*.ldf
*.ndf

# SQLite
*.db
*.sqlite
*.sqlite3

# ===========================
# Docker
# ===========================

# Docker files (keep Dockerfiles but ignore build artifacts)
.dockerignore

# ===========================
# IDE and Editor files
# ===========================

# JetBrains Rider
.idea/
*.sln.iml

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===========================
# OS generated files
# ===========================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===========================
# Application specific
# ===========================

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Configuration files with sensitive data
appsettings.Production.json
appsettings.Development.json
secrets.json

# SSL certificates
*.pfx
*.p12
*.key
*.crt
*.pem

# Backup files
*.bak
*.backup
*.old

# Temporary files
*.tmp
*.temp

# Published files
publish/
PublishProfiles/

# Package files
*.nupkg
*.snupkg

# Setup scripts output
start-*.bat
docker-*.bat

# User uploads
uploads/
files/

# Cache directories
.cache/
cache/

# ===========================
# Custom project ignores
# ===========================

# Custom word lists (if any sensitive data)
# custom-words.json

# User avatars and uploads
wwwroot/uploads/
wwwroot/avatars/

# Generated migration files (optional - comment out if you want to track them)
# Migrations/

# Test results
TestResults/
test-results/

# Performance profiling
*.psess
*.vsp
*.vspx
*.sap

# Azure specific
*.pubxml
*.publishsettings

# Local development database
LocalDatabase.db

# Redis dump
dump.rdb

# Webpack bundles info
webpack-stats.json

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache
