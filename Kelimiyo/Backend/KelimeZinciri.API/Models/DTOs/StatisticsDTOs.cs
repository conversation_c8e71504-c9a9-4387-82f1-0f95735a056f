namespace KelimeZinciri.API.Models.DTOs
{
    public class GameResultDto
    {
        public int Score { get; set; }
        public bool IsWin { get; set; }
        public int WordsUsed { get; set; }
        public int TotalWordLength { get; set; }
        public string? LongestWord { get; set; }
        public int GameTime { get; set; }
    }

    public class UserStatistics
    {
        public int UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string? Avatar { get; set; }
        public int GamesPlayed { get; set; }
        public int GamesWon { get; set; }
        public double WinRate { get; set; }
        public int TotalScore { get; set; }
        public int BestScore { get; set; }
        public double AverageScore { get; set; }
        public int TotalWordsUsed { get; set; }
        public double AverageWordLength { get; set; }
        public string LongestWord { get; set; } = string.Empty;
        public int FastestGameTime { get; set; }
        public int CurrentStreak { get; set; }
        public int BestStreak { get; set; }
        public int Rank { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class GameResult
    {
        public int Score { get; set; }
        public bool IsWin { get; set; }
        public int WordsUsed { get; set; }
        public int TotalWordLength { get; set; }
        public string LongestWord { get; set; } = string.Empty;
        public int GameTime { get; set; }
    }
}
