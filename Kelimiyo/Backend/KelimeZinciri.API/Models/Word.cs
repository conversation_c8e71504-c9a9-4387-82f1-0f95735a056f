using System.ComponentModel.DataAnnotations;

namespace KelimeZinciri.API.Models
{
    public class Word
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Text { get; set; } = string.Empty;
        
        public int Length { get; set; }
        public char FirstLetter { get; set; }
        public char LastLetter { get; set; }
        
        public WordCategory Category { get; set; } = WordCategory.General;
        public WordDifficulty Difficulty { get; set; } = WordDifficulty.Easy;
        
        public int UsageCount { get; set; } = 0;
        public bool IsActive { get; set; } = true;
        public bool IsVerified { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastUsedAt { get; set; }
        
        // For scoring
        public int BasePoints { get; set; } = 1;
        public double Frequency { get; set; } = 1.0; // Word frequency in Turkish
    }

    public class WordValidation
    {
        public int Id { get; set; }
        public string Word { get; set; } = string.Empty;
        public bool IsValid { get; set; }
        public string? Reason { get; set; }
        public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;
        public int? ValidatedByUserId { get; set; }
        
        public virtual User? ValidatedByUser { get; set; }
    }

    public class CustomWord
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Text { get; set; } = string.Empty;
        public string? Definition { get; set; }
        public WordValidationStatus Status { get; set; } = WordValidationStatus.Pending;
        
        public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;
        public DateTime? ReviewedAt { get; set; }
        public int? ReviewedByUserId { get; set; }
        public string? ReviewNotes { get; set; }
        
        public virtual User User { get; set; } = null!;
        public virtual User? ReviewedByUser { get; set; }
    }

    public enum WordCategory
    {
        General = 0,
        Animal = 1,
        Food = 2,
        Place = 3,
        Object = 4,
        Person = 5,
        Action = 6,
        Adjective = 7,
        Science = 8,
        Technology = 9,
        Sports = 10,
        Art = 11,
        Nature = 12
    }

    public enum WordDifficulty
    {
        Easy = 1,    // 3-5 letters, common words
        Medium = 2,  // 6-8 letters, moderate words
        Hard = 3     // 9+ letters, rare words
    }

    public enum WordValidationStatus
    {
        Pending = 0,
        Approved = 1,
        Rejected = 2,
        UnderReview = 3
    }

    // DTO Classes for API responses
    public class WordDto
    {
        public string Text { get; set; } = string.Empty;
        public int Length { get; set; }
        public char FirstLetter { get; set; }
        public char LastLetter { get; set; }
        public string Category { get; set; } = string.Empty;
        public string Difficulty { get; set; } = string.Empty;
        public int Points { get; set; }
    }

    public class WordValidationDto
    {
        public string Word { get; set; } = string.Empty;
        public bool IsValid { get; set; }
        public string? Reason { get; set; }
        public int Points { get; set; }
        public WordDto? WordInfo { get; set; }
    }
}
