using Microsoft.EntityFrameworkCore;
using KelimeZinciri.API.Models;

namespace KelimeZinciri.API.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<User> Users { get; set; }
        public DbSet<Game> Games { get; set; }
        public DbSet<GamePlayer> GamePlayers { get; set; }
        public DbSet<GameMove> GameMoves { get; set; }
        public DbSet<Statistics> Statistics { get; set; }
        public DbSet<Word> Words { get; set; }
        public DbSet<WordValidation> WordValidations { get; set; }
        public DbSet<CustomWord> CustomWords { get; set; }
        public DbSet<Achievement> Achievements { get; set; }
        public DbSet<Friendship> Friendships { get; set; }
        public DbSet<FriendRequest> FriendRequests { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // PostgreSQL specific configurations
            modelBuilder.HasPostgresExtension("uuid-ossp");
            modelBuilder.HasPostgresExtension("pg_trgm"); // For text search optimization

            // User configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Username).HasMaxLength(50);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.FirstName).HasMaxLength(50);
                entity.Property(e => e.LastName).HasMaxLength(50);
            });

            // Game configuration
            modelBuilder.Entity<Game>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.RoomCode).IsUnique();
                entity.Property(e => e.RoomCode).HasMaxLength(10);
                entity.Property(e => e.CurrentWord).HasMaxLength(50);

                entity.HasOne(e => e.CreatedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // GamePlayer configuration
            modelBuilder.Entity<GamePlayer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.GameId, e.UserId }).IsUnique();

                entity.HasOne(e => e.Game)
                    .WithMany(g => g.GamePlayers)
                    .HasForeignKey(e => e.GameId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany(u => u.GamePlayers)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // GameMove configuration
            modelBuilder.Entity<GameMove>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Word).HasMaxLength(50);

                entity.HasOne(e => e.Game)
                    .WithMany(g => g.Moves)
                    .HasForeignKey(e => e.GameId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Statistics configuration
            modelBuilder.Entity<Statistics>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.UserId).IsUnique();

                entity.HasOne(e => e.User)
                    .WithOne(u => u.Statistics)
                    .HasForeignKey<Statistics>(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.AverageWordLength).HasPrecision(10, 2);
                entity.Property(e => e.LongestWord).HasMaxLength(50);
            });

            // Word configuration
            modelBuilder.Entity<Word>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Text).IsUnique();
                entity.HasIndex(e => e.FirstLetter);
                entity.HasIndex(e => e.LastLetter);
                entity.HasIndex(e => new { e.FirstLetter, e.Length });

                entity.Property(e => e.Text).HasMaxLength(50);
                entity.Property(e => e.Frequency).HasPrecision(10, 6);
            });

            // WordValidation configuration
            modelBuilder.Entity<WordValidation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Word).HasMaxLength(50);
                entity.Property(e => e.Reason).HasMaxLength(200);

                entity.HasOne(e => e.ValidatedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.ValidatedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // CustomWord configuration
            modelBuilder.Entity<CustomWord>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Text).HasMaxLength(50);
                entity.Property(e => e.Definition).HasMaxLength(500);
                entity.Property(e => e.ReviewNotes).HasMaxLength(200);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.ReviewedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.ReviewedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Achievement configuration
            modelBuilder.Entity<Achievement>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Type).HasMaxLength(50);
                entity.Property(e => e.Title).HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Icon).HasMaxLength(10);

                entity.HasOne(e => e.User)
                    .WithMany(u => u.Achievements)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Friendship configuration
            modelBuilder.Entity<Friendship>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.UserId, e.FriendId }).IsUnique();

                entity.HasOne(e => e.User)
                    .WithMany(u => u.Friendships)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Friend)
                    .WithMany(u => u.FriendOf)
                    .HasForeignKey(e => e.FriendId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // FriendRequest configuration
            modelBuilder.Entity<FriendRequest>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.FromUserId, e.ToUserId }).IsUnique();

                entity.HasOne(e => e.FromUser)
                    .WithMany(u => u.SentFriendRequests)
                    .HasForeignKey(e => e.FromUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ToUser)
                    .WithMany(u => u.ReceivedFriendRequests)
                    .HasForeignKey(e => e.ToUserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
