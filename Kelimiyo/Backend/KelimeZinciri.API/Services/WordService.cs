using Microsoft.EntityFrameworkCore;
using KelimeZinciri.API.Data;
using KelimeZinciri.API.Models;
using Newtonsoft.Json;

namespace KelimeZinciri.API.Services
{
    public interface IWordService
    {
        Task<WordValidationDto> ValidateWordAsync(string word, char? requiredStartLetter = null);
        Task<List<WordDto>> GetRandomWordsAsync(int count = 10, WordDifficulty? difficulty = null);
        Task<WordDto?> GetRandomWordByLetterAsync(char startLetter, WordDifficulty? difficulty = null);
        Task<bool> AddCustomWordAsync(int userId, string word, string? definition = null);
        Task<List<WordDto>> SearchWordsAsync(string query, int limit = 20);
        Task<int> CalculateWordPointsAsync(string word, TimeSpan responseTime, GameDifficulty gameDifficulty);
        Task LoadWordsFromJsonAsync(string jsonFilePath);
    }

    public class WordService : IWordService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<WordService> _logger;

        public WordService(ApplicationDbContext context, ILogger<WordService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<WordValidationDto> ValidateWordAsync(string word, char? requiredStartLetter = null)
        {
            try
            {
                word = word.Trim();

                // Basic validation
                if (string.IsNullOrEmpty(word) || word.Length < 3)
                {
                    return new WordValidationDto
                    {
                        Word = word,
                        IsValid = false,
                        Reason = "Kelime en az 3 harf olmalıdır.",
                        Points = 0
                    };
                }

                // Check required start letter
                if (requiredStartLetter.HasValue && word[0] != requiredStartLetter.Value)
                {
                    return new WordValidationDto
                    {
                        Word = word,
                        IsValid = false,
                        Reason = $"Kelime '{requiredStartLetter.Value}' harfi ile başlamalıdır.",
                        Points = 0
                    };
                }

                // Check if word exists in database
                var dbWord = await _context.Words
                    .FirstOrDefaultAsync(w => w.Text == word && w.IsActive && w.IsVerified);

                if (dbWord == null)
                {
                    // Log for potential addition to dictionary
                    await LogWordValidationAsync(word, false, "Kelime sözlükte bulunamadı.");

                    return new WordValidationDto
                    {
                        Word = word,
                        IsValid = false,
                        Reason = "Kelime sözlükte bulunamadı.",
                        Points = 0
                    };
                }

                // Update usage count
                dbWord.UsageCount++;
                dbWord.LastUsedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                // Calculate points
                var points = CalculateBasePoints(dbWord);

                return new WordValidationDto
                {
                    Word = word,
                    IsValid = true,
                    Points = points,
                    WordInfo = new WordDto
                    {
                        Text = dbWord.Text,
                        Length = dbWord.Length,
                        FirstLetter = dbWord.FirstLetter,
                        LastLetter = dbWord.LastLetter,
                        Category = dbWord.Category.ToString(),
                        Difficulty = dbWord.Difficulty.ToString(),
                        Points = points
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating word: {Word}", word);
                return new WordValidationDto
                {
                    Word = word,
                    IsValid = false,
                    Reason = "Kelime doğrulama sırasında hata oluştu.",
                    Points = 0
                };
            }
        }

        public async Task<List<WordDto>> GetRandomWordsAsync(int count = 10, WordDifficulty? difficulty = null)
        {
            try
            {
                var query = _context.Words.Where(w => w.IsActive && w.IsVerified);

                if (difficulty.HasValue)
                {
                    query = query.Where(w => w.Difficulty == difficulty.Value);
                }

                var words = await query
                    .OrderBy(w => Guid.NewGuid()) // Random order
                    .Take(count)
                    .Select(w => new WordDto
                    {
                        Text = w.Text,
                        Length = w.Length,
                        FirstLetter = w.FirstLetter,
                        LastLetter = w.LastLetter,
                        Category = w.Category.ToString(),
                        Difficulty = w.Difficulty.ToString(),
                        Points = w.BasePoints
                    })
                    .ToListAsync();

                return words;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting random words");
                return new List<WordDto>();
            }
        }

        public async Task<WordDto?> GetRandomWordByLetterAsync(char startLetter, WordDifficulty? difficulty = null)
        {
            try
            {
                var query = _context.Words
                    .Where(w => w.IsActive && w.IsVerified && w.FirstLetter == startLetter);

                if (difficulty.HasValue)
                {
                    query = query.Where(w => w.Difficulty == difficulty.Value);
                }

                var word = await query
                    .OrderBy(w => Guid.NewGuid())
                    .Select(w => new WordDto
                    {
                        Text = w.Text,
                        Length = w.Length,
                        FirstLetter = w.FirstLetter,
                        LastLetter = w.LastLetter,
                        Category = w.Category.ToString(),
                        Difficulty = w.Difficulty.ToString(),
                        Points = w.BasePoints
                    })
                    .FirstOrDefaultAsync();

                return word;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting random word by letter: {Letter}", startLetter);
                return null;
            }
        }

        public async Task<bool> AddCustomWordAsync(int userId, string word, string? definition = null)
        {
            try
            {
                word = word.Trim();

                // Check if word already exists
                var existingWord = await _context.Words
                    .FirstOrDefaultAsync(w => w.Text == word);

                if (existingWord != null)
                {
                    return false; // Word already exists
                }

                // Check if custom word already submitted
                var existingCustomWord = await _context.CustomWords
                    .FirstOrDefaultAsync(cw => cw.Text == word && cw.UserId == userId);

                if (existingCustomWord != null)
                {
                    return false; // Already submitted
                }

                var customWord = new CustomWord
                {
                    UserId = userId,
                    Text = word,
                    Definition = definition,
                    Status = WordValidationStatus.Pending,
                    SubmittedAt = DateTime.UtcNow
                };

                _context.CustomWords.Add(customWord);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding custom word: {Word} by user: {UserId}", word, userId);
                return false;
            }
        }

        public async Task<List<WordDto>> SearchWordsAsync(string query, int limit = 20)
        {
            try
            {
                query = query.Trim();

                var words = await _context.Words
                    .Where(w => w.IsActive && w.IsVerified && w.Text.Contains(query))
                    .OrderBy(w => w.Text.Length)
                    .ThenBy(w => w.Text)
                    .Take(limit)
                    .Select(w => new WordDto
                    {
                        Text = w.Text,
                        Length = w.Length,
                        FirstLetter = w.FirstLetter,
                        LastLetter = w.LastLetter,
                        Category = w.Category.ToString(),
                        Difficulty = w.Difficulty.ToString(),
                        Points = w.BasePoints
                    })
                    .ToListAsync();

                return words;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching words with query: {Query}", query);
                return new List<WordDto>();
            }
        }

        public async Task<int> CalculateWordPointsAsync(string word, TimeSpan responseTime, GameDifficulty gameDifficulty)
        {
            try
            {
                var dbWord = await _context.Words
                    .FirstOrDefaultAsync(w => w.Text == word);

                if (dbWord == null) return 0;

                var basePoints = CalculateBasePoints(dbWord);

                // Time bonus (faster response = more points)
                var timeBonus = CalculateTimeBonus(responseTime);

                // Difficulty multiplier
                var difficultyMultiplier = gameDifficulty switch
                {
                    GameDifficulty.Easy => 1.0,
                    GameDifficulty.Medium => 1.5,
                    GameDifficulty.Hard => 2.0,
                    _ => 1.0
                };

                var totalPoints = (int)((basePoints + timeBonus) * difficultyMultiplier);
                return Math.Max(1, totalPoints); // Minimum 1 point
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating points for word: {Word}", word);
                return 1;
            }
        }

        public async Task LoadWordsFromJsonAsync(string jsonFilePath)
        {
            try
            {
                if (!File.Exists(jsonFilePath))
                {
                    _logger.LogWarning("Word file not found: {FilePath}", jsonFilePath);
                    return;
                }

                var jsonContent = await File.ReadAllTextAsync(jsonFilePath);
                var wordList = JsonConvert.DeserializeObject<List<string>>(jsonContent);

                if (wordList == null || !wordList.Any())
                {
                    _logger.LogWarning("No words found in file: {FilePath}", jsonFilePath);
                    return;
                }

                // Clean and filter words first
                var cleanWords = wordList
                    .Select(w => w.Trim())
                    .Where(w => !string.IsNullOrEmpty(w) && w.Length >= 3)
                    .Distinct()
                    .ToList();

                // Get existing words in one query
                var existingWords = await _context.Words
                    .Where(w => cleanWords.Contains(w.Text))
                    .Select(w => w.Text)
                    .ToHashSetAsync();

                // Create words that don't exist
                var wordsToAdd = new List<Word>();

                foreach (var cleanWord in cleanWords)
                {
                    if (existingWords.Contains(cleanWord)) continue;

                    var word = new Word
                    {
                        Text = cleanWord,
                        Length = cleanWord.Length,
                        FirstLetter = cleanWord[0],
                        LastLetter = cleanWord[cleanWord.Length - 1],
                        Category = WordCategory.General,
                        Difficulty = DetermineWordDifficulty(cleanWord),
                        BasePoints = CalculateBasePointsByLength(cleanWord.Length),
                        Frequency = 1.0,
                        IsActive = true,
                        IsVerified = true,
                        CreatedAt = DateTime.UtcNow
                    };

                    wordsToAdd.Add(word);
                }

                if (wordsToAdd.Any())
                {
                    _context.Words.AddRange(wordsToAdd);
                    await _context.SaveChangesAsync();
                    _logger.LogInformation("Added {Count} words to database", wordsToAdd.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading words from JSON file: {FilePath}", jsonFilePath);
            }
        }

        private int CalculateBasePoints(Word word)
        {
            var lengthPoints = word.Length;
            var difficultyMultiplier = word.Difficulty switch
            {
                WordDifficulty.Easy => 1,
                WordDifficulty.Medium => 2,
                WordDifficulty.Hard => 3,
                _ => 1
            };

            // Rare words get bonus points
            var frequencyBonus = word.Frequency < 0.1 ? 2 : 0;

            return Math.Max(1, lengthPoints * difficultyMultiplier + frequencyBonus);
        }

        private int CalculateTimeBonus(TimeSpan responseTime)
        {
            var seconds = responseTime.TotalSeconds;
            return seconds switch
            {
                <= 5 => 5,   // Very fast
                <= 10 => 3,  // Fast
                <= 15 => 1,  // Normal
                _ => 0       // Slow
            };
        }

        private WordDifficulty DetermineWordDifficulty(string word)
        {
            return word.Length switch
            {
                <= 5 => WordDifficulty.Easy,
                <= 8 => WordDifficulty.Medium,
                _ => WordDifficulty.Hard
            };
        }

        private int CalculateBasePointsByLength(int length)
        {
            return length switch
            {
                <= 4 => 1,
                <= 6 => 2,
                <= 8 => 3,
                _ => 4
            };
        }

        private async Task LogWordValidationAsync(string word, bool isValid, string? reason = null)
        {
            try
            {
                var validation = new WordValidation
                {
                    Word = word,
                    IsValid = isValid,
                    Reason = reason,
                    ValidatedAt = DateTime.UtcNow
                };

                _context.WordValidations.Add(validation);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging word validation for: {Word}", word);
            }
        }
    }
}
