{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=KelimeZinciriDB;Username=kelimiyo;Password=********;Include Error Detail=true"}, "Jwt": {"Key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Super_Secret_Key_2024_Very_Long_And_Secure_Key_For_JWT_Token_Generation", "Issuer": "KelimeZinciri.API", "Audience": "KelimeZinciri.Client", "ExpiryInDays": 7}, "Redis": {"ConnectionString": "localhost:6379"}, "Game": {"DefaultTimeLimit": 30, "MaxPlayers": 8, "MinPlayers": 2, "MaxRounds": 20, "DefaultRounds": 10}, "Word": {"MinLength": 3, "MaxLength": 20, "CacheExpiryMinutes": 60}}