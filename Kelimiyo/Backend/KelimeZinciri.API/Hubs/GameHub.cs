using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using KelimeZinciri.API.Services;
using System.Security.Claims;

namespace KelimeZinciri.API.Hubs
{
    [Authorize]
    public class GameHub : Hub
    {
        private readonly IGameService _gameService;
        private readonly IWordService _wordService;
        private readonly ILogger<GameHub> _logger;

        public GameHub(IGameService gameService, IWordService wordService, ILogger<GameHub> logger)
        {
            _gameService = gameService;
            _wordService = wordService;
            _logger = logger;
        }

        public async Task JoinGameRoom(string roomCode)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    await Clients.Caller.SendAsync("Error", "Kullanıcı kimliği bulunamadı.");
                    return;
                }

                var game = await _gameService.GetGameByRoomCodeAsync(roomCode);
                if (game == null)
                {
                    await Clients.Caller.SendAsync("Error", "<PERSON>yun odası bulunamadı.");
                    return;
                }

                // Add user to SignalR group
                await Groups.AddToGroupAsync(Context.ConnectionId, $"Game_{game.Id}");

                // Notify OTHER players (not the joining player)
                await Clients.OthersInGroup($"Game_{game.Id}").SendAsync("PlayerJoined", new
                {
                    userId = userId,
                    username = Context.User?.Identity?.Name,
                    message = $"{Context.User?.Identity?.Name} oyuna katıldı."
                });

                // Send current game state to the joining player
                await Clients.Caller.SendAsync("GameState", game);

                _logger.LogInformation("User {UserId} joined game room {RoomCode}", userId, roomCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error joining game room {RoomCode}", roomCode);
                await Clients.Caller.SendAsync("Error", "Oyun odasına katılırken hata oluştu.");
            }
        }

        public async Task LeaveGameRoom(string roomCode)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null) return;

                var game = await _gameService.GetGameByRoomCodeAsync(roomCode);
                if (game == null) return;

                // Remove user from SignalR group
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Game_{game.Id}");

                // Notify OTHER players (not the leaving player)
                await Clients.OthersInGroup($"Game_{game.Id}").SendAsync("PlayerLeft", new
                {
                    userId = userId,
                    username = Context.User?.Identity?.Name,
                    message = $"{Context.User?.Identity?.Name} oyundan ayrıldı."
                });

                _logger.LogInformation("User {UserId} left game room {RoomCode}", userId, roomCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error leaving game room {RoomCode}", roomCode);
            }
        }

        // NOTE: StartGame is now handled by API Controller to avoid duplicate events
        // This method is kept for backward compatibility but should not be used

        // NOTE: SubmitWord is now handled by API Controller to avoid duplicate events
        // This method is kept for backward compatibility but should not be used

        public async Task SendChatMessage(int gameId, string message)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null) return;

                if (string.IsNullOrWhiteSpace(message) || message.Length > 200)
                {
                    await Clients.Caller.SendAsync("Error", "Mesaj geçersiz.");
                    return;
                }

                // Broadcast chat message to all players in the game
                await Clients.Group($"Game_{gameId}").SendAsync("ChatMessage", new
                {
                    userId = userId,
                    username = Context.User?.Identity?.Name,
                    message = message.Trim(),
                    timestamp = DateTime.UtcNow
                });

                _logger.LogInformation("User {UserId} sent chat message in game {GameId}", userId, gameId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending chat message in game {GameId}", gameId);
            }
        }

        public async Task RequestGameState(int gameId)
        {
            try
            {
                var game = await _gameService.GetGameAsync(gameId);
                if (game != null)
                {
                    await Clients.Caller.SendAsync("GameState", game);
                }
                else
                {
                    await Clients.Caller.SendAsync("Error", "Oyun bulunamadı.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error requesting game state for game {GameId}", gameId);
                await Clients.Caller.SendAsync("Error", "Oyun durumu alınırken hata oluştu.");
            }
        }

        public async Task ValidateWord(string word, char? requiredStartLetter = null)
        {
            try
            {
                var validation = await _wordService.ValidateWordAsync(word, requiredStartLetter);
                
                await Clients.Caller.SendAsync("WordValidation", new
                {
                    word = word,
                    isValid = validation.IsValid,
                    reason = validation.Reason,
                    points = validation.Points,
                    wordInfo = validation.WordInfo
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating word '{Word}'", word);
                await Clients.Caller.SendAsync("Error", "Kelime doğrulanırken hata oluştu.");
            }
        }

        public async Task PlayerTyping(int gameId, bool isTyping)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null) return;

                // Notify other players that this user is typing
                await Clients.OthersInGroup($"Game_{gameId}").SendAsync("PlayerTyping", new
                {
                    userId = userId,
                    username = Context.User?.Identity?.Name,
                    isTyping = isTyping
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling player typing in game {GameId}", gameId);
            }
        }

        public override async Task OnConnectedAsync()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId != null)
                {
                    _logger.LogInformation("User {UserId} connected to GameHub", userId);
                }
                await base.OnConnectedAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error on user connection");
            }
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId != null)
                {
                    _logger.LogInformation("User {UserId} disconnected from GameHub", userId);
                }

                if (exception != null)
                {
                    _logger.LogError(exception, "User disconnected with exception");
                }

                await base.OnDisconnectedAsync(exception);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error on user disconnection");
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return null;
        }
    }
}
