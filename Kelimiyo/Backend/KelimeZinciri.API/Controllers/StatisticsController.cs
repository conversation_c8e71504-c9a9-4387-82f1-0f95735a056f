using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using KelimeZinciri.API.Services;
using KelimeZinciri.API.Models.DTOs;
using System.Security.Claims;

namespace KelimeZinciri.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class StatisticsController : ControllerBase
    {
        private readonly IStatisticsService _statisticsService;
        private readonly ILogger<StatisticsController> _logger;

        public StatisticsController(IStatisticsService statisticsService, ILogger<StatisticsController> logger)
        {
            _statisticsService = statisticsService;
            _logger = logger;
        }

        [HttpGet("user/{userId}")]
        public async Task<IActionResult> GetUserStatistics(int userId)
        {
            try
            {
                var statistics = await _statisticsService.GetUserStatisticsAsync(userId);

                return Ok(new
                {
                    success = true,
                    data = statistics
                });
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting statistics for user {UserId}", userId);
                return StatusCode(500, new { message = "İstatistikler alınırken hata oluştu." });
            }
        }

        [HttpGet("my")]
        public async Task<IActionResult> GetMyStatistics()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var statistics = await _statisticsService.GetMyStatisticsAsync(userId.Value);

                return Ok(new
                {
                    success = true,
                    data = statistics
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting my statistics");
                return StatusCode(500, new { message = "İstatistikler alınırken hata oluştu." });
            }
        }

        [HttpPost("update-game-result")]
        public async Task<IActionResult> UpdateGameResult([FromBody] GameResultDto gameResultDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var gameResult = new GameResult
                {
                    Score = gameResultDto.Score,
                    IsWin = gameResultDto.IsWin,
                    WordsUsed = gameResultDto.WordsUsed,
                    TotalWordLength = gameResultDto.TotalWordLength,
                    LongestWord = gameResultDto.LongestWord ?? "",
                    GameTime = gameResultDto.GameTime
                };

                await _statisticsService.UpdateGameStatisticsAsync(userId.Value, gameResult);

                return Ok(new
                {
                    success = true,
                    message = "İstatistikler güncellendi."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating game statistics");
                return StatusCode(500, new { message = "İstatistikler güncellenirken hata oluştu." });
            }
        }

        [HttpGet("achievements")]
        public async Task<IActionResult> GetMyAchievements()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var achievements = await _statisticsService.GetUserAchievementsAsync(userId.Value);

                return Ok(new
                {
                    success = true,
                    data = achievements
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user achievements");
                return StatusCode(500, new { message = "Başarımlar alınırken hata oluştu." });
            }
        }

        [HttpGet("achievements/{userId}")]
        public async Task<IActionResult> GetUserAchievements(int userId)
        {
            try
            {
                var achievements = await _statisticsService.GetUserAchievementsAsync(userId);

                return Ok(new
                {
                    success = true,
                    data = achievements
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting achievements for user {UserId}", userId);
                return StatusCode(500, new { message = "Başarımlar alınırken hata oluştu." });
            }
        }

        [HttpPost("check-achievements")]
        public async Task<IActionResult> CheckAchievements()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                await _statisticsService.CheckAndAwardAchievementsAsync(userId.Value);

                return Ok(new
                {
                    success = true,
                    message = "Başarımlar kontrol edildi."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking achievements");
                return StatusCode(500, new { message = "Başarımlar kontrol edilirken hata oluştu." });
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return null;
        }
    }

}
