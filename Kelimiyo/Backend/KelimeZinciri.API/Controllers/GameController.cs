using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using KelimeZinciri.API.Services;
using KelimeZinciri.API.Models;
using KelimeZinciri.API.Hubs;
using System.Security.Claims;

namespace KelimeZinciri.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class GameController : ControllerBase
    {
        private readonly IGameService _gameService;
        private readonly IWordService _wordService;
        private readonly ILogger<GameController> _logger;
        private readonly IHubContext<GameHub> _hubContext;

        public GameController(IGameService gameService, IWordService wordService, ILogger<GameController> logger, IHubContext<GameHub> hubContext)
        {
            _gameService = gameService;
            _wordService = wordService;
            _logger = logger;
            _hubContext = hubContext;
        }

        [HttpPost("create-room")]
        public async Task<IActionResult> CreateRoom([FromBody] CreateGameDto createGameDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Validate input
                if (!createGameDto.IsSinglePlayer && (createGameDto.MaxPlayers < 2 || createGameDto.MaxPlayers > 8))
                {
                    return BadRequest(new { message = "Çok oyunculu oyunlarda oyuncu sayısı 2-8 arasında olmalıdır." });
                }

                if (createGameDto.IsSinglePlayer && createGameDto.MaxPlayers != 1)
                {
                    return BadRequest(new { message = "Tek oyunculu oyunlarda oyuncu sayısı 1 olmalıdır." });
                }

                if (createGameDto.TimeLimit < 10 || createGameDto.TimeLimit > 120)
                {
                    return BadRequest(new { message = "Süre limiti 10-120 saniye arasında olmalıdır." });
                }

                if (createGameDto.MaxRounds < 1 || createGameDto.MaxRounds > 20)
                {
                    return BadRequest(new { message = "Round sayısı 1-20 arasında olmalıdır." });
                }

                var game = await _gameService.CreateGameAsync(userId.Value, createGameDto);

                return Ok(new
                {
                    success = true,
                    game = game,
                    message = "Oyun odası oluşturuldu."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating game room");
                return StatusCode(500, new { message = "Oyun odası oluşturulurken hata oluştu." });
            }
        }

        [HttpPost("join-room/{roomCode}")]
        public async Task<IActionResult> JoinRoom(string roomCode)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                if (string.IsNullOrWhiteSpace(roomCode))
                {
                    return BadRequest(new { message = "Oda kodu gereklidir." });
                }

                var game = await _gameService.JoinGameAsync(userId.Value, roomCode.ToUpperInvariant());

                if (game == null)
                {
                    return NotFound(new { message = "Oyun odası bulunamadı veya dolu." });
                }

                // NOTE: PlayerJoined notification is handled by GameHub.JoinGameRoom
                // to prevent duplicate messages

                return Ok(new
                {
                    success = true,
                    game = game,
                    message = "Oyun odasına katıldınız."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error joining game room: {RoomCode}", roomCode);
                return StatusCode(500, new { message = "Oyun odasına katılırken hata oluştu." });
            }
        }

        [HttpPost("{gameId}/start")]
        public async Task<IActionResult> StartGame(int gameId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var success = await _gameService.StartGameAsync(gameId, userId.Value);

                if (!success)
                {
                    return BadRequest(new { message = "Oyun başlatılamadı. Yeterli oyuncu yok veya yetkiniz bulunmuyor." });
                }

                // Get updated game state and notify all players via SignalR
                var game = await _gameService.GetGameAsync(gameId);
                if (game != null)
                {
                    await _hubContext.Clients.Group($"Game_{gameId}").SendAsync("GameStarted", new
                    {
                        game = game,
                        message = "Oyun başladı!",
                        currentPlayerTurn = game.CurrentPlayerTurnId,
                        requiredStartLetter = game.RequiredStartLetter
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "Oyun başlatıldı."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting game: {GameId}", gameId);
                return StatusCode(500, new { message = "Oyun başlatılırken hata oluştu." });
            }
        }

        [HttpPost("{gameId}/submit-word")]
        public async Task<IActionResult> SubmitWord(int gameId, [FromBody] SubmitWordDto submitWordDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                if (string.IsNullOrWhiteSpace(submitWordDto.Word))
                {
                    return BadRequest(new { message = "Kelime gereklidir." });
                }

                var result = await _gameService.SubmitWordAsync(gameId, userId.Value, submitWordDto.Word);

                if (!result.Success)
                {
                    // Notify only the caller about word rejection via SignalR
                    await _hubContext.Clients.User(userId.ToString()).SendAsync("WordRejected", new
                    {
                        word = submitWordDto.Word,
                        reason = result.Message,
                        isValidWord = result.IsValidWord
                    });

                    return BadRequest(new
                    {
                        success = false,
                        message = result.Message,
                        isValidWord = result.IsValidWord
                    });
                }

                // Get updated game state and notify all players via SignalR
                var game = await _gameService.GetGameAsync(gameId);
                if (game != null)
                {
                    await _hubContext.Clients.Group($"Game_{gameId}").SendAsync("WordSubmitted", new
                    {
                        userId = userId,
                        username = User?.Identity?.Name,
                        word = submitWordDto.Word,
                        points = result.Points,
                        nextPlayerTurnId = result.NextPlayerTurnId,
                        requiredStartLetter = result.RequiredStartLetter,
                        gameStatus = result.GameStatus,
                        game = game
                    });

                    // If game ended, notify players
                    if (result.GameStatus == "Finished")
                    {
                        await _hubContext.Clients.Group($"Game_{gameId}").SendAsync("GameEnded", new
                        {
                            game = game,
                            message = "Oyun bitti!"
                        });
                    }
                }

                return Ok(new
                {
                    success = true,
                    message = result.Message,
                    points = result.Points,
                    nextPlayerTurnId = result.NextPlayerTurnId,
                    requiredStartLetter = result.RequiredStartLetter,
                    gameStatus = result.GameStatus
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting word for game: {GameId}", gameId);
                return StatusCode(500, new { message = "Kelime gönderilirken hata oluştu." });
            }
        }

        [HttpGet("validate-word/{word}")]
        public async Task<IActionResult> ValidateWord(string word, [FromQuery] char? requiredStartLetter = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(word))
                {
                    return BadRequest(new { message = "Kelime gereklidir." });
                }

                var validation = await _wordService.ValidateWordAsync(word, requiredStartLetter);

                return Ok(new
                {
                    success = true,
                    validation = validation
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating word: {Word}", word);
                return StatusCode(500, new { message = "Kelime doğrulanırken hata oluştu." });
            }
        }

        [HttpGet("{gameId}")]
        public async Task<IActionResult> GetGame(int gameId)
        {
            try
            {
                var game = await _gameService.GetGameAsync(gameId);

                if (game == null)
                {
                    return NotFound(new { message = "Oyun bulunamadı." });
                }

                return Ok(new
                {
                    success = true,
                    game = game
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting game: {GameId}", gameId);
                return StatusCode(500, new { message = "Oyun bilgileri alınırken hata oluştu." });
            }
        }

        [HttpGet("room/{roomCode}")]
        public async Task<IActionResult> GetGameByRoomCode(string roomCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(roomCode))
                {
                    return BadRequest(new { message = "Oda kodu gereklidir." });
                }

                var game = await _gameService.GetGameByRoomCodeAsync(roomCode.ToUpperInvariant());

                if (game == null)
                {
                    return NotFound(new { message = "Oyun odası bulunamadı." });
                }

                return Ok(new
                {
                    success = true,
                    game = game
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting game by room code: {RoomCode}", roomCode);
                return StatusCode(500, new { message = "Oyun bilgileri alınırken hata oluştu." });
            }
        }

        [HttpPost("{gameId}/leave")]
        public async Task<IActionResult> LeaveGame(int gameId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var success = await _gameService.LeaveGameAsync(gameId, userId.Value);

                if (!success)
                {
                    return BadRequest(new { message = "Oyundan çıkılamadı." });
                }

                return Ok(new
                {
                    success = true,
                    message = "Oyundan çıktınız."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error leaving game: {GameId}", gameId);
                return StatusCode(500, new { message = "Oyundan çıkılırken hata oluştu." });
            }
        }

        [HttpGet("active")]
        public async Task<IActionResult> GetActiveGames()
        {
            try
            {
                var games = await _gameService.GetActiveGamesAsync();

                return Ok(new
                {
                    success = true,
                    games = games
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active games");
                return StatusCode(500, new { message = "Aktif oyunlar alınırken hata oluştu." });
            }
        }

        [HttpGet("my-games")]
        public async Task<IActionResult> GetMyGames([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 50) pageSize = 10;

                var games = await _gameService.GetUserGamesAsync(userId.Value, page, pageSize);

                return Ok(new
                {
                    success = true,
                    games = games,
                    page = page,
                    pageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user games");
                return StatusCode(500, new { message = "Oyunlarınız alınırken hata oluştu." });
            }
        }

        [HttpGet("random-words")]
        public async Task<IActionResult> GetRandomWords([FromQuery] int count = 10, [FromQuery] string? difficulty = null)
        {
            try
            {
                WordDifficulty? wordDifficulty = null;
                if (!string.IsNullOrEmpty(difficulty) && Enum.TryParse<WordDifficulty>(difficulty, true, out var parsedDifficulty))
                {
                    wordDifficulty = parsedDifficulty;
                }

                if (count < 1 || count > 50) count = 10;

                var words = await _wordService.GetRandomWordsAsync(count, wordDifficulty);

                return Ok(new
                {
                    success = true,
                    words = words
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting random words");
                return StatusCode(500, new { message = "Rastgele kelimeler alınırken hata oluştu." });
            }
        }

        [HttpPost("add-custom-word")]
        public async Task<IActionResult> AddCustomWord([FromBody] AddCustomWordDto addCustomWordDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                if (string.IsNullOrWhiteSpace(addCustomWordDto.Word))
                {
                    return BadRequest(new { message = "Kelime gereklidir." });
                }

                var success = await _wordService.AddCustomWordAsync(userId.Value, addCustomWordDto.Word, addCustomWordDto.Definition);

                if (!success)
                {
                    return BadRequest(new { message = "Kelime eklenemedi. Zaten mevcut olabilir." });
                }

                return Ok(new
                {
                    success = true,
                    message = "Kelime inceleme için gönderildi."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding custom word");
                return StatusCode(500, new { message = "Kelime eklenirken hata oluştu." });
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return null;
        }
    }

    // DTOs
    public class SubmitWordDto
    {
        public string Word { get; set; } = string.Empty;
    }

    public class AddCustomWordDto
    {
        public string Word { get; set; } = string.Empty;
        public string? Definition { get; set; }
    }
}
