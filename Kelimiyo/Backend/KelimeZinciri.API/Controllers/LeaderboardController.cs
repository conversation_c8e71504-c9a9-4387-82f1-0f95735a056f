using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using KelimeZinciri.API.Services;
using System.Security.Claims;

namespace KelimeZinciri.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class LeaderboardController : ControllerBase
    {
        private readonly ILeaderboardService _leaderboardService;
        private readonly ILogger<LeaderboardController> _logger;

        public LeaderboardController(ILeaderboardService leaderboardService, ILogger<LeaderboardController> logger)
        {
            _leaderboardService = leaderboardService;
            _logger = logger;
        }

        [HttpGet("global")]
        public async Task<IActionResult> GetGlobalLeaderboard([FromQuery] int limit = 50)
        {
            try
            {
                if (limit <= 0 || limit > 100)
                {
                    limit = 50;
                }

                var leaderboard = await _leaderboardService.GetGlobalLeaderboardAsync(limit);
                
                return Ok(new
                {
                    success = true,
                    data = leaderboard,
                    count = leaderboard.Count,
                    type = "global"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting global leaderboard");
                return StatusCode(500, new { message = "Liderlik tablosu alınırken hata oluştu." });
            }
        }

        [HttpGet("weekly")]
        public async Task<IActionResult> GetWeeklyLeaderboard([FromQuery] int limit = 50)
        {
            try
            {
                if (limit <= 0 || limit > 100)
                {
                    limit = 50;
                }

                var leaderboard = await _leaderboardService.GetWeeklyLeaderboardAsync(limit);
                
                return Ok(new
                {
                    success = true,
                    data = leaderboard,
                    count = leaderboard.Count,
                    type = "weekly",
                    period = "Son 7 gün"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting weekly leaderboard");
                return StatusCode(500, new { message = "Haftalık liderlik tablosu alınırken hata oluştu." });
            }
        }

        [HttpGet("monthly")]
        public async Task<IActionResult> GetMonthlyLeaderboard([FromQuery] int limit = 50)
        {
            try
            {
                if (limit <= 0 || limit > 100)
                {
                    limit = 50;
                }

                var leaderboard = await _leaderboardService.GetMonthlyLeaderboardAsync(limit);
                
                return Ok(new
                {
                    success = true,
                    data = leaderboard,
                    count = leaderboard.Count,
                    type = "monthly",
                    period = "Son 30 gün"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting monthly leaderboard");
                return StatusCode(500, new { message = "Aylık liderlik tablosu alınırken hata oluştu." });
            }
        }

        [HttpGet("my-rank")]
        public async Task<IActionResult> GetMyRank()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                var userRank = await _leaderboardService.GetUserRankAsync(userId.Value);
                
                if (userRank == null)
                {
                    return NotFound(new { message = "Kullanıcı sıralaması bulunamadı." });
                }

                return Ok(new
                {
                    success = true,
                    data = userRank
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user rank");
                return StatusCode(500, new { message = "Kullanıcı sıralaması alınırken hata oluştu." });
            }
        }

        [HttpGet("user/{userId}/rank")]
        public async Task<IActionResult> GetUserRank(int userId)
        {
            try
            {
                var userRank = await _leaderboardService.GetUserRankAsync(userId);
                
                if (userRank == null)
                {
                    return NotFound(new { message = "Kullanıcı sıralaması bulunamadı." });
                }

                return Ok(new
                {
                    success = true,
                    data = userRank
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting rank for user {UserId}", userId);
                return StatusCode(500, new { message = "Kullanıcı sıralaması alınırken hata oluştu." });
            }
        }

        [HttpGet("friends")]
        public async Task<IActionResult> GetFriendsLeaderboard([FromQuery] int limit = 20)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı." });
                }

                if (limit <= 0 || limit > 50)
                {
                    limit = 20;
                }

                var leaderboard = await _leaderboardService.GetFriendsLeaderboardAsync(userId.Value, limit);
                
                return Ok(new
                {
                    success = true,
                    data = leaderboard,
                    count = leaderboard.Count,
                    type = "friends",
                    message = leaderboard.Count == 0 ? "Henüz arkadaş listenizde kimse yok." : null
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting friends leaderboard");
                return StatusCode(500, new { message = "Arkadaş liderlik tablosu alınırken hata oluştu." });
            }
        }

        [HttpGet("top-players")]
        public async Task<IActionResult> GetTopPlayers([FromQuery] int limit = 10)
        {
            try
            {
                if (limit <= 0 || limit > 20)
                {
                    limit = 10;
                }

                var topPlayers = await _leaderboardService.GetGlobalLeaderboardAsync(limit);
                
                return Ok(new
                {
                    success = true,
                    data = topPlayers,
                    count = topPlayers.Count,
                    type = "top-players"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top players");
                return StatusCode(500, new { message = "En iyi oyuncular alınırken hata oluştu." });
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return null;
        }
    }
}
