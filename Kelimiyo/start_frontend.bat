@echo off
echo ========================================
echo    Kelime Zinciri Frontend Başlatıcı
echo ========================================
echo.

:: Set variables
set FRONTEND_DIR=%~dp0Frontend

echo [1/5] Node.js versiyonu kontrol ediliyor...
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo HATA: Node.js bulunamadı!
    echo Lütfen Node.js'i indirin: https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✓ Node.js kurulu
    node --version
)

echo.
echo [2/5] Frontend dizinine geçiliyor...
cd /d "%FRONTEND_DIR%"
if %errorLevel% neq 0 (
    echo HATA: Frontend dizini bulunamadı: %FRONTEND_DIR%
    pause
    exit /b 1
)

echo.
echo [3/5] Package.json kontrol ediliyor...
if not exist "package.json" (
    echo HATA: package.json bulunamadı!
    pause
    exit /b 1
) else (
    echo ✓ package.json mevcut
)

echo.
echo [4/5] Node modules kontrol ediliyor...
if not exist "node_modules" (
    echo Node modules bulunamadı. Paketler yükleniyor...
    npm install
    if %errorLevel% neq 0 (
        echo HATA: Paket yüklemesi başarısız!
        pause
        exit /b 1
    )
) else (
    echo ✓ Node modules mevcut
    echo Paketler güncelleniyor...
    npm install
)

echo.
echo [5/5] Frontend başlatılıyor...
echo.
echo ========================================
echo     Frontend Bilgileri
echo ========================================
echo Port: 3000
echo URL: http://localhost:3000
echo Backend Proxy: https://localhost:7001
echo ========================================
echo.
echo Backend'in çalıştığından emin olun!
echo Backend başlatmak için: cd Backend/KelimeZinciri.API && dotnet run
echo.
echo Frontend başlatılıyor...
echo Tarayıcınızda http://localhost:3000 açılacak
echo.
echo Durdurmak için Ctrl+C tuşlarına basın
echo ========================================
echo.

:: Start the frontend
npm run dev

echo.
echo Frontend durduruldu.
pause

cd /d "%~dp0"
exit /b 0
