# Kelimiyo - Oyun Tasarım Referans Dokümanı

## Yönetici Özeti
**Kelimiyo** (ayrıca "<PERSON><PERSON><PERSON> Zinci<PERSON>" o<PERSON><PERSON> da bilinir), eğitici dil öğrenimini rekabetçi sosyal oyun deneyimi ile birleştiren gerçek zamanlı çok oyunculu Türkçe kelime zinciri oyunudur. Oyun, 40.000+ doğrulanmış kelimeden oluşan kapsamlı Türkçe sözlük kullanarak, her yeni kelimenin önceki kelimenin son harfi ile başlaması gereken kelime zincirleri oluşturma konusunda oyuncuları zorlar.

---

## 1. Oyun Başlığı
**Ana Başlık:** Kelimiyo  
**Alternatif Başlık:** Kelime Zinciri  
**Proje Kod Adı:** KelimeZinciri.API

---

## 2. Temel Amaç
Birincil amaç, aşağıdaki kriterleri karşılayan **geçerli Türkçe kelime zincirleri oluşturmaktır**:
- Gerekli başlangıç harfi ile başlamalı (önceki kelimenin son harfi)
- Oyunun sözlüğünden doğrulanmış Türkçe kelimeler olmalı
- Mevcut oyun oturumunda daha önce kullanılmamış olmalı
- Zaman sınırı içinde gönderilmeli

Oyuncular stratejik kelime seçimi, hız ve Türkçe kelime bilgisi aracılığıyla en yüksek skoru elde etmek için yarışırlar.

---

## 3. Oyun Teması ve Türü

### Tür Sınıflandırması
- **Ana Tür:** Kelime/Kelime Dağarcığı Oyunu
- **İkincil Tür:** Gerçek Zamanlı Çok Oyunculu Strateji
- **Eğitim Kategorisi:** Dil Öğrenimi/Türkçe Kelime Dağarcığı

### Görsel ve Kavramsal Tema
- **Kültürel Tema:** Türk dili ve kültürü
- **Görsel Stil:** Gradient arka planlar ve kart tabanlı düzenlemeler ile modern, temiz arayüz
- **Renk Paleti:** Mor-mavi gradyanlar ve vurgu renkleri (başarı için yeşil, hatalar için kırmızı)
- **Tasarım Felsefesi:** Erişilebilir, eğitici ve sosyal olarak ilgi çekici

---

## 4. Anlatı ve Ortam

### Ima Edilen Anlatı
Oyun geleneksel bir hikayeden yoksun olsa da, şu bağlamda işler:
- **Kültürel Koruma:** Türkçe dil yeterliliğini ve kelime dağarcığı genişletmeyi teşvik etme
- **Eğitim Misyonu:** Oyuncuların Türkçe dil becerilerini geliştirmelerine yardım etme
- **Sosyal Öğrenme:** Türkçe dil meraklıları topluluğu oluşturma

### Ortam Bağlamı
- **Dil Bağlamı:** Türkçe dilsel ortam
- **Sosyal Bağlam:** Rekabetçi öğrenme topluluğu
- **Eğitim Bağlamı:** Kelime dağarcığı oluşturma ve dil ustalığı

---

## 5. İlerleme Sistemi

### Zorluk Seviyeleri
1. **Kolay:** 3-5 harfli kelimeler, yaygın kelime dağarcığı, 1.0x puan çarpanı
2. **Orta:** 6-8 harfli kelimeler, orta seviye kelime dağarcığı, 1.5x puan çarpanı  
3. **Zor:** 9+ harfli kelimeler, nadir kelime dağarcığı, 2.0x puan çarpanı

### Oyun Yapısı
- **Turlar:** Oyunlar birden fazla turdan oluşur (varsayılan: 10 tur)
- **Sıra Tabanlı İlerleme:** Çok oyunculu modda oyuncular sırayla oynar
- **Zaman Sınırları:** Oyun başına yapılandırılabilir zaman kısıtlamaları (varsayılan: tur başına 30 saniye)
- **Skor Birikimi:** Kazananları belirlemek için turlar boyunca puanlar birikir

### İlerleme Mekanikleri
- **Kullanıcı İstatistikleri:** Oynanan oyunları, kazanılan oyunları, en iyi skorları takip etme
- **Başarım Sistemi:** Rozet ve başarımları açma
- **Liderlik Tabloları:** Global, haftalık ve aylık sıralamalar
- **Arkadaş Sistemi:** Sosyal bağlantılar ve özel yarışmalar

---

## 6. Kazanma/Kaybetme Koşulları

### Zafer Koşulları
- **Ana Kazanma:** Oyun tamamlandığında en yüksek toplam skor
- **Puanlama Faktörleri:**
  - Kelime uzunluğu (uzun kelimeler = daha fazla puan)
  - Kelime zorluğu/nadir olma durumu (nadir kelimeler = bonus puan)
  - Yanıt hızı (hızlı gönderimler = zaman bonusu)
  - Oyun zorluğu çarpanı

### Başarısızlık Koşulları
- **Geçersiz Kelime Gönderimi:** Türkçe sözlükte olmayan kelime
- **Yanlış Başlangıç Harfi:** Kelime gerekli harf ile başlamıyor
- **Tekrarlanan Kelime:** Mevcut oyunda zaten kullanılan kelime
- **Zaman Aşımı:** Zaman sınırı içinde gönderim yapamama
- **Oyunu Terk Etme:** Oyunu tamamlamadan önce ayrılma

### Puan Hesaplama Sistemi
```
Temel Puanlar = Kelime Uzunluğu × Zorluk Çarpanı + Sıklık Bonusu
Zaman Bonusu = Hız tabanlı bonus (≤5 saniye için 5 puan, ≤10 saniye için 3 puan, vb.)
Nihai Skor = (Temel Puanlar + Zaman Bonusu) × Oyun Zorluğu Çarpanı
```

---

## 7. Oyuncu Rolü ve Perspektifi

### Oyuncu Rolü
- **Kimlik:** Türkçe dil öğrencisi/yarışmacısı
- **Sorumluluk:** Kelime zincirini devam ettirmek için geçerli Türkçe kelimeler göndermek
- **Etki:** Puanları maksimize etmek ve rakipleri zorlamak için stratejik kelime seçimi

### Oyun Perspektifi
- **Arayüz Türü:** Web tabanlı GUI (React frontend)
- **Görünüm Modu:** Kart tabanlı düzenleme ile yukarıdan görünüm arayüzü
- **Etkileşim Modeli:** Gerçek zamanlı geri bildirim ile metin girişi
- **Sosyal Öğeler:** Canlı oyuncu durumu ve sohbet göstergeleri ile çok oyunculu odalar

---

## 8. Temel Etkileşimler ve Mekanikler

### Ana Mekanikler
1. **Kelime Girişi:** Klavye girişi kullanarak Türkçe kelimeler yazma
2. **Gerçek Zamanlı Doğrulama:** Kelime geçerliliği konusunda anlık geri bildirim
3. **Tur Yönetimi:** Çok oyunculu oyunlarda yapılandırılmış sıra alma
4. **Oda Oluşturma/Katılma:** Oda kodları ile sosyal lobi sistemi
5. **Canlı Güncellemeler:** SignalR aracılığıyla gerçek zamanlı oyun durumu senkronizasyonu

### İkincil Mekanikler
- **Yazma Göstergeleri:** Diğer oyuncuların ne zaman yazdığını gösterme
- **Skor Takibi:** Canlı skor güncellemeleri ve liderlik tabloları
- **Oyun Geçmişi:** Son hamleleri ve kelime zincirlerini gözden geçirme
- **Profil Yönetimi:** İstatistikler ve başarımlar ile kullanıcı hesapları

### Teknik Etkileşimler
- **Kimlik Doğrulama:** JWT tabanlı kullanıcı giriş sistemi
- **Gerçek Zamanlı İletişim:** SignalR WebSocket bağlantıları
- **Veritabanı İşlemleri:** Kalıcı veri depolama için PostgreSQL
- **Kelime Doğrulama:** Sunucu tarafı Türkçe sözlük doğrulaması

---

## 9. Ton ve Stil

### Genel Ton
- **Eğitici:** Öğrenmeyi ve kelime dağarcığı geliştirmeyi teşvik eder
- **Rekabetçi:** Dostane rekabeti ve beceri geliştirmeyi teşvik eder
- **Sosyal:** Topluluk oluşturma ve paylaşılan deneyimleri vurgular
- **Erişilebilir:** Her Türkçe yeterlilik seviyesindeki oyuncuları karşılar

### Stil Özellikleri
- **Modern ve Temiz:** Akıcı animasyonlar ile çağdaş web tasarımı
- **Kültürel Saygı:** Türk dili ve kültürünü kutlar
- **Kullanıcı Dostu:** Net geri bildirim mekanizmaları ile sezgisel arayüz
- **Profesyonel:** Kurumsal düzeyde mimari ile iyi yapılandırılmış kod tabanı

### Duygusal Tasarım Hedefleri
- **Katılım:** Oyuncuları kelime dağarcıklarını geliştirmeye motive etme
- **Başarı:** Puanlama ve ilerleme yoluyla tatmin sağlama
- **Bağlantı:** Çok oyunculu etkileşim yoluyla sosyal bağlar kurma
- **Öğrenme:** Türkçe dil edinimini keyifli ve ödüllendirici hale getirme

---

## Teknik Mimari Özeti

### Frontend Yığını
- **Framework:** Vite yapı sistemi ile React 18
- **Stil:** Framer Motion animasyonları ile Tailwind CSS
- **Durum Yönetimi:** React Context API
- **Gerçek Zamanlı:** Canlı güncellemeler için SignalR istemcisi

### Backend Yığını
- **API:** .NET 9.0 Core Web API
- **Veritabanı:** Entity Framework Core ile PostgreSQL
- **Kimlik Doğrulama:** JWT token tabanlı güvenlik
- **Gerçek Zamanlı:** Çok oyunculu iletişim için SignalR hub'ları

### Temel Özellikler
- **Ölçeklenebilir Mimari:** Docker desteği ile konteynerleştirilmiş
- **Kapsamlı Sözlük:** 40.000+ doğrulanmış Türkçe kelime
- **Gerçek Zamanlı Çok Oyunculu:** Oyun odası başına 4 oyuncuya kadar
- **Progresif Web Uygulaması:** Mobil uyumlu tasarım
- **İstatistik ve Analitik:** Detaylı oyuncu performans takibi

---

*Bu doküman, Kelimiyo'nun oyun tasarımı, mekanikleri ve teknik uygulamasını anlamak için kesin referans görevi görür. Oyun özellikleri, UI/UX iyileştirmeleri veya teknik geliştirmeler üzerinde çalışan tüm ekip üyeleri tarafından danışılmalıdır.*
