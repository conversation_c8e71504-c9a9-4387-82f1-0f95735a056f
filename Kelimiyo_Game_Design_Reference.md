# Kelimiyo - Game Design Reference Document

## Executive Summary
**Kelimiyo** (also known as "<PERSON><PERSON><PERSON> Zinciri") is a real-time multiplayer Turkish word chain game that combines educational language learning with competitive social gameplay. The game challenges players to create word chains where each new word must begin with the last letter of the previous word, using a comprehensive Turkish dictionary of 40,000+ verified words.

---

## 1. Game Title
**Primary Title:** Kelimiyo  
**Alternative Title:** <PERSON><PERSON><PERSON> (Turkish Word Chain)  
**Project Codename:** KelimeZinciri.API

---

## 2. Core Objective
The primary objective is to **create valid Turkish word chains** by submitting words that:
- Begin with the required starting letter (last letter of the previous word)
- Are verified Turkish words from the game's dictionary
- Have not been used previously in the current game session
- Are submitted within the time limit

Players compete to achieve the highest score through strategic word selection, speed, and knowledge of Turkish vocabulary.

---

## 3. Game Theme and Genre

### Genre Classification
- **Primary Genre:** Word/Vocabulary Game
- **Secondary Genre:** Real-time Multiplayer Strategy
- **Educational Category:** Language Learning/Turkish Vocabulary

### Visual and Conceptual Theme
- **Cultural Theme:** Turkish language and culture
- **Visual Style:** Modern, clean UI with gradient backgrounds and card-based layouts
- **Color Palette:** Purple-blue gradients with accent colors (green for success, red for errors)
- **Design Philosophy:** Accessible, educational, and socially engaging

---

## 4. Narrative and Setting

### Implied Narrative
While the game lacks a traditional storyline, it operates within the context of:
- **Cultural Preservation:** Promoting Turkish language proficiency and vocabulary expansion
- **Educational Mission:** Helping players improve their Turkish language skills
- **Social Learning:** Creating a community of Turkish language enthusiasts

### Setting Context
- **Language Context:** Turkish linguistic environment
- **Social Context:** Competitive learning community
- **Educational Context:** Vocabulary building and language mastery

---

## 5. Progression System

### Difficulty Levels
1. **Easy (Kolay):** 3-5 letter words, common vocabulary, 1.0x point multiplier
2. **Medium (Orta):** 6-8 letter words, moderate vocabulary, 1.5x point multiplier  
3. **Hard (Zor):** 9+ letter words, rare vocabulary, 2.0x point multiplier

### Game Structure
- **Rounds:** Games consist of multiple rounds (default: 10 rounds)
- **Turn-based Progression:** Players take turns in multiplayer mode
- **Time Limits:** Configurable time constraints per game (default: 30 seconds per turn)
- **Score Accumulation:** Points accumulate across rounds to determine winners

### Advancement Mechanics
- **User Statistics:** Track games played, wins, best scores
- **Achievement System:** Unlock badges and accomplishments
- **Leaderboards:** Global, weekly, and monthly rankings
- **Friend System:** Social connections and private competitions

---

## 6. Win/Lose Conditions

### Victory Conditions
- **Primary Win:** Highest total score at game completion
- **Scoring Factors:**
  - Word length (longer words = more points)
  - Word difficulty/rarity (rare words = bonus points)
  - Response speed (faster submissions = time bonus)
  - Game difficulty multiplier

### Failure Conditions
- **Invalid Word Submission:** Word not in Turkish dictionary
- **Incorrect Starting Letter:** Word doesn't begin with required letter
- **Duplicate Word:** Word already used in current game
- **Time Expiration:** Failure to submit within time limit
- **Game Abandonment:** Leaving game before completion

### Point Calculation System
```
Base Points = Word Length × Difficulty Multiplier + Frequency Bonus
Time Bonus = Speed-based bonus (5 points for ≤5 seconds, 3 for ≤10 seconds, etc.)
Final Score = (Base Points + Time Bonus) × Game Difficulty Multiplier
```

---

## 7. Player Role and Perspective

### Player Role
- **Identity:** Turkish language learner/competitor
- **Responsibility:** Submit valid Turkish words to continue the word chain
- **Agency:** Choose words strategically to maximize points and challenge opponents

### Game Perspective
- **Interface Type:** Web-based GUI (React frontend)
- **View Mode:** Top-down interface with card-based layout
- **Interaction Model:** Text input with real-time feedback
- **Social Elements:** Multiplayer rooms with live player status and chat indicators

---

## 8. Key Interactions and Mechanics

### Core Mechanics
1. **Word Input:** Type Turkish words using keyboard input
2. **Real-time Validation:** Instant feedback on word validity
3. **Turn Management:** Structured turn-taking in multiplayer games
4. **Room Creation/Joining:** Social lobby system with room codes
5. **Live Updates:** Real-time game state synchronization via SignalR

### Secondary Mechanics
- **Typing Indicators:** Show when other players are typing
- **Score Tracking:** Live score updates and leaderboards
- **Game History:** Review recent moves and word chains
- **Profile Management:** User accounts with statistics and achievements

### Technical Interactions
- **Authentication:** JWT-based user login system
- **Real-time Communication:** SignalR WebSocket connections
- **Database Operations:** PostgreSQL for persistent data storage
- **Word Validation:** Server-side Turkish dictionary verification

---

## 9. Tone and Style

### Overall Tone
- **Educational:** Promotes learning and vocabulary improvement
- **Competitive:** Encourages friendly competition and skill development
- **Social:** Emphasizes community building and shared experiences
- **Accessible:** Welcoming to players of all Turkish proficiency levels

### Style Characteristics
- **Modern and Clean:** Contemporary web design with smooth animations
- **Culturally Respectful:** Celebrates Turkish language and culture
- **User-Friendly:** Intuitive interface with clear feedback mechanisms
- **Professional:** Well-structured codebase with enterprise-level architecture

### Emotional Design Goals
- **Engagement:** Keep players motivated to improve their vocabulary
- **Achievement:** Provide satisfaction through scoring and progression
- **Connection:** Foster social bonds through multiplayer interaction
- **Learning:** Make Turkish language acquisition enjoyable and rewarding

---

## Technical Architecture Summary

### Frontend Stack
- **Framework:** React 18 with Vite build system
- **Styling:** Tailwind CSS with Framer Motion animations
- **State Management:** React Context API
- **Real-time:** SignalR client for live updates

### Backend Stack
- **API:** .NET 9.0 Core Web API
- **Database:** PostgreSQL with Entity Framework Core
- **Authentication:** JWT token-based security
- **Real-time:** SignalR hubs for multiplayer communication

### Key Features
- **Scalable Architecture:** Containerized with Docker support
- **Comprehensive Dictionary:** 40,000+ verified Turkish words
- **Real-time Multiplayer:** Up to 4 players per game room
- **Progressive Web App:** Mobile-responsive design
- **Statistics & Analytics:** Detailed player performance tracking

---

*This document serves as the definitive reference for understanding Kelimiyo's game design, mechanics, and technical implementation. It should be consulted by all team members working on game features, UI/UX improvements, or technical enhancements.*
